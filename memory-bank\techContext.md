# Technology Context: Tech Stack & Dependencies

## Core Technologies

### Backend Stack
- **Python 3.8+**: Core programming language
- **Flask 2.x**: Web framework for REST API and static serving
- **pyodbc**: SQL Server database connectivity
- **xlsxwriter**: Excel file generation with formatting
- **datetime**: Date/time manipulation for business logic

### Frontend Stack
- **HTML5**: Semantic markup structure
- **CSS3**: Styling with Grid and Flexbox
- **JavaScript (ES6+)**: Client-side logic and API communication
- **Bootstrap 5.1.3**: Responsive UI framework
- **Font Awesome 6.0**: Icon library

### Database
- **Microsoft SQL Server**: Primary database (VenusHR14)
- **ODBC Connection**: Database connectivity protocol
- **Read-Only Access**: Security constraint for data integrity

### External Integrations
- **Google Apps Script**: Spreadsheet sync functionality
- **Google Sheets API**: Data export and collaboration

## Development Setup

### Python Dependencies
```txt
# requirements.txt
Flask==2.3.3
pyodbc==4.0.39
xlsxwriter==3.1.9
python-dotenv==1.0.0

# requirements_web.txt (additional)
flask-cors==4.0.0
gunicorn==21.2.0
```

### Installation Process
```bash
# Windows setup
install_dependencies.bat

# Manual installation
pip install -r requirements.txt
pip install -r requirements_web.txt
```

### Database Configuration
```python
# Database connection settings
DB_CONFIG = {
    'server': 'your_server_name',
    'database': 'VenusHR14',
    'driver': '{ODBC Driver 17 for SQL Server}',
    'trusted_connection': 'yes'  # Windows Authentication
}

# Connection string format
CONN_STR = (
    f"DRIVER={driver};"
    f"SERVER={server};"
    f"DATABASE={database};"
    f"Trusted_Connection=yes"
)
```

## Technical Constraints

### Database Constraints
- **Read-Only Access**: No INSERT, UPDATE, or DELETE operations
- **Windows Authentication**: Integrated security required
- **ODBC Driver**: Specific driver version dependency
- **Connection Timeout**: 30-second timeout for queries
- **Concurrent Connections**: Limited connection pool

### Performance Constraints
- **Large Datasets**: Performance degradation with >1000 employees
- **Query Complexity**: Multi-table JOINs with date range filtering
- **Export Size**: Excel files limited by browser memory
- **Browser Compatibility**: IE11+ support requirements

### Security Constraints
- **Database Credentials**: Windows-integrated authentication only
- **CORS Policy**: Restricted cross-origin requests
- **Input Validation**: All user inputs sanitized
- **SQL Injection Prevention**: Parameterized queries mandatory

## Browser Compatibility

### Supported Browsers
- **Chrome 80+**: Full feature support
- **Firefox 75+**: Full feature support
- **Safari 13+**: Full feature support (with polyfills)
- **Edge 80+**: Full feature support
- **IE 11**: Limited support (no CSS Grid)

### Feature Support Matrix
| Feature | Chrome | Firefox | Safari | Edge | IE11 |
|---------|--------|---------|--------|------|------|
| CSS Grid | ✓ | ✓ | ✓ | ✓ | ✗ |
| Sticky Headers | ✓ | ✓ | ✓ | ✓ | ✗ |
| Fetch API | ✓ | ✓ | ✓ | ✓ | Polyfill |
| ES6 Classes | ✓ | ✓ | ✓ | ✓ | ✗ |

## Development Environment

### Required Software
- **Python 3.8+**: Core runtime
- **Visual Studio Code**: Recommended IDE
- **SQL Server Management Studio**: Database management
- **Git**: Version control
- **PowerShell**: Windows script execution

### Development Dependencies
```txt
# Development-only packages
flask-debugtoolbar==0.13.1
pytest==7.4.0
pytest-flask==1.2.0
```

### Environment Variables
```env
# .env file configuration
FLASK_ENV=development
FLASK_DEBUG=True
DATABASE_SERVER=localhost\SQLEXPRESS
DATABASE_NAME=VenusHR14
LOG_LEVEL=DEBUG
```

## Deployment Architecture

### Production Requirements
- **Windows Server**: IIS or standalone Flask server
- **SQL Server Access**: Network connectivity to VenusHR14
- **Python Runtime**: 3.8+ with required packages
- **SSL Certificate**: HTTPS support for Google Sheets integration

### Deployment Options
```python
# Option 1: IIS with FastCGI
# web.config configuration for IIS deployment

# Option 2: Standalone Flask
if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, debug=False)

# Option 3: Gunicorn (Linux/Docker)
gunicorn -w 4 -b 0.0.0.0:5000 web_app:app
```

## API Design

### RESTful Endpoints
```python
# Core API structure
GET  /api/months                    # Available months
GET  /api/monthly-report           # Monthly summary data
GET  /api/monthly-grid             # Monthly grid data
GET  /api/monthly-grid-by-station  # Station-grouped grid
POST /api/export-grid              # Export functionality
GET  /api/attendance               # Custom date range data
```

### Response Format
```json
{
    "success": true,
    "data": {
        // Actual response data
    },
    "timestamp": "2024-01-01T00:00:00.000Z",
    "metadata": {
        "total_records": 100,
        "query_time": "0.5s"
    }
}
```

## Security Measures

### Input Validation
```python
# Date validation pattern
def validate_date_range(start_date, end_date):
    """Validate date inputs and ranges"""
    if not start_date or not end_date:
        raise ValueError("Start and end dates required")
    
    if start_date > end_date:
        raise ValueError("Start date must be before end date")
    
    # Limit query range to prevent performance issues
    if (end_date - start_date).days > 366:
        raise ValueError("Date range cannot exceed one year")
```

### SQL Injection Prevention
```python
# Parameterized query pattern
query = """
    SELECT * FROM HR_T_TAMachine_Summary 
    WHERE BusinessCode = ? 
    AND AttendDate BETWEEN ? AND ?
"""
cursor.execute(query, (business_code, start_date, end_date))
```

## Monitoring & Logging

### Logging Configuration
```python
import logging

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('attendance_app.log'),
        logging.StreamHandler()
    ]
)
```

### Performance Monitoring
- **Query execution time**: Database performance tracking
- **Response time**: API endpoint performance
- **Memory usage**: Large dataset handling
- **Error rates**: Exception tracking and alerting

## Backup & Recovery

### Data Backup Strategy
- **Database**: Read-only access, no backup responsibility
- **Export Files**: Temporary storage in `exports/` directory
- **Application Logs**: Rotated daily with 30-day retention
- **Configuration**: Version controlled in Git

### Disaster Recovery
- **Application Recovery**: Redeploy from Git repository
- **Database Access**: Verify VenusHR14 connectivity
- **Dependencies**: Reinstall Python packages
- **Configuration**: Restore environment variables 