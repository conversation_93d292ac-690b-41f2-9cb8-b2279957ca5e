#!/usr/bin/env python3
"""
Test script to diagnose sync functionality issues
"""

import requests
import json
import sys

def test_sync_endpoint():
    # Test data
    test_data = {
        "sync_url": "https://script.google.com/macros/s/AKfycbxaf8IKT4gkAv81fS0w9-901cRyKW4F-jvQ9E6jOMm8JpIkt0bmzlp9n3S3rdUN9Hcn/exec",
        "action": "sync_attendance",
        "data": [
            {
                "employeeId": "TEST001",
                "employeeName": "Test Employee",
                "date": "2025-01-15",
                "dayOfWeek": "Wednesday",
                "shift": "Morning",
                "checkIn": "08:00:00",
                "checkOut": "17:00:00",
                "regularHours": 8,
                "overtimeHours": 1,
                "totalHours": 9
            }
        ]
    }
    
    print("=" * 50)
    print("TESTING SYNC ENDPOINT")
    print("=" * 50)
    
    # Test 1: Direct call to Google Apps Script
    print("\n1. Testing direct call to Google Apps Script...")
    try:
        params = {
            'action': test_data['action'],
            'data': json.dumps(test_data['data'])
        }
        
        response = requests.get(test_data['sync_url'], params=params, timeout=30)
        print(f"Status: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            print("✓ Direct Google Apps Script call successful")
        else:
            print("✗ Direct Google Apps Script call failed")
            
    except Exception as e:
        print(f"✗ Direct call error: {e}")
    
    # Test 2: Call through Flask proxy
    print("\n2. Testing Flask proxy endpoint...")
    try:
        response = requests.post(
            'http://localhost:5000/api/sync-to-spreadsheet',
            json=test_data,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        print(f"Status: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            print("✓ Flask proxy call successful")
        else:
            print("✗ Flask proxy call failed")
            
    except Exception as e:
        print(f"✗ Flask proxy error: {e}")
    
    # Test 3: Test daily grid sync (the most likely scenario)
    print("\n3. Testing daily grid sync...")
    daily_test_data = {
        "sync_url": "https://script.google.com/macros/s/AKfycbxaf8IKT4gkAv81fS0w9-901cRyKW4F-jvQ9E6jOMm8JpIkt0bmzlp9n3S3rdUN9Hcn/exec",
        "action": "sync_daily_grid",
        "data": [
            {
                "no": 1,
                "employeeId": "TEST001",
                "employeeName": "Test Employee",
                "year": 2025,
                "month": 1,
                "monthName": "January",
                "daysInMonth": 31,
                "dailyHours": {
                    "1": "(7) | (-)",
                    "1_status": {
                        "status": "complete",
                        "check_in_only": False,
                        "check_out_only": False,
                        "complete_record": True,
                        "regular": 7,
                        "overtime": 0
                    }
                },
                "taskCode": "TSK001",
                "machineCode": "MCH001", 
                "expenseCode": "EXP001"
            }
        ]
    }
    
    try:
        response = requests.post(
            'http://localhost:5000/api/sync-to-spreadsheet',
            json=daily_test_data,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        print(f"Status: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            print("✓ Daily grid sync successful")
        else:
            print("✗ Daily grid sync failed")
            
    except Exception as e:
        print(f"✗ Daily grid sync error: {e}")

    print("\n" + "=" * 50)
    print("Test completed. Check the results above.")
    print("=" * 50)

if __name__ == "__main__":
    test_sync_endpoint() 