#!/usr/bin/env python3
"""
Test script to verify Google Apps Script sync functionality fix.
This script tests the sync endpoint with sample data to ensure the 400 BAD REQUEST error is resolved.
"""

import requests
import json
import sys
from datetime import datetime

# Test configuration
FLASK_APP_URL = "http://localhost:5000"
GOOGLE_APPS_SCRIPT_URL = "https://script.google.com/macros/s/AKfycbxaf8IKT4gkAv81fS0w9-901cRyKW4F-jvQ9E6jOMm8JpIkt0bmzlp9n3S3rdUN9Hcn/exec"

def test_sync_endpoint():
    """Test the sync-to-spreadsheet endpoint with sample data."""
    
    print("=" * 60)
    print("TESTING GOOGLE APPS SCRIPT SYNC FIX")
    print("=" * 60)
    
    # Sample test data (minimal structure for testing)
    test_data = [
        {
            "no": 1,
            "employeeId": "PTRJ.TEST001",
            "employeeName": "Test Employee 1",
            "year": 2025,
            "month": 1,
            "monthName": "January",
            "daysInMonth": 31,
            "dailyHours": {
                "1": "(7) | (-)",
                "2": "(7) | (-)",
                "3": "(7) | (-)",
                "4": "(7) | (-)",
                "5": "(7) | (-)",
                "6": "OFF",
                "7": "OFF"
            },
            "taskCode": "TSK001",
            "machineCode": "MCH001", 
            "expenseCode": "EXP001"
        },
        {
            "no": 2,
            "employeeId": "PTRJ.TEST002",
            "employeeName": "Test Employee 2",
            "year": 2025,
            "month": 1,
            "monthName": "January",
            "daysInMonth": 31,
            "dailyHours": {
                "1": "(6) | (1)",
                "2": "(7) | (-)",
                "3": "(5) | (-)",
                "4": "(7) | (2)",
                "5": "(7) | (-)",
                "6": "OFF",
                "7": "OFF"
            },
            "taskCode": "TSK002",
            "machineCode": "MCH002",
            "expenseCode": "EXP002"
        }
    ]
    
    # Test payload for Flask sync endpoint
    sync_payload = {
        "sync_url": GOOGLE_APPS_SCRIPT_URL,
        "action": "sync_daily_grid",
        "data": test_data
    }
    
    print(f"Testing Flask app at: {FLASK_APP_URL}")
    print(f"Google Apps Script URL: {GOOGLE_APPS_SCRIPT_URL}")
    print(f"Test data records: {len(test_data)}")
    print(f"Action: {sync_payload['action']}")
    print()
    
    try:
        print("Sending sync request...")
        response = requests.post(
            f"{FLASK_APP_URL}/api/sync-to-spreadsheet",
            json=sync_payload,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        print(f"Response Status Code: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        print()
        
        if response.status_code == 200:
            print("✅ SUCCESS: Sync request completed successfully!")
            try:
                result = response.json()
                print("Response JSON:")
                print(json.dumps(result, indent=2))
                
                if result.get('success'):
                    print("\n🎉 SYNC FUNCTIONALITY IS NOW WORKING!")
                    print("The 400 BAD REQUEST error has been resolved.")
                else:
                    print(f"\n⚠️  Sync completed but with issues: {result.get('error', 'Unknown error')}")
                    
            except json.JSONDecodeError:
                print("Response is not valid JSON:")
                print(response.text)
                
        elif response.status_code == 400:
            print("❌ FAILED: Still getting 400 BAD REQUEST error")
            try:
                error_data = response.json()
                print("Error details:")
                print(json.dumps(error_data, indent=2))
            except:
                print("Error response:")
                print(response.text)
                
        else:
            print(f"❌ FAILED: Unexpected status code {response.status_code}")
            print("Response:")
            print(response.text)
            
    except requests.exceptions.ConnectionError:
        print("❌ FAILED: Could not connect to Flask app")
        print("Make sure the Flask app is running on http://localhost:5000")
        return False
        
    except requests.exceptions.Timeout:
        print("❌ FAILED: Request timed out")
        print("The Google Apps Script might be taking too long to respond")
        return False
        
    except Exception as e:
        print(f"❌ FAILED: Unexpected error: {str(e)}")
        return False
    
    return response.status_code == 200

def test_direct_google_script():
    """Test the Google Apps Script directly to verify it's working."""
    
    print("\n" + "=" * 60)
    print("TESTING GOOGLE APPS SCRIPT DIRECTLY")
    print("=" * 60)
    
    # Simple test data for direct Google Apps Script call
    test_data = [
        {
            "no": 1,
            "employeeId": "PTRJ.DIRECT001",
            "employeeName": "Direct Test Employee",
            "year": 2025,
            "month": 1,
            "monthName": "January",
            "daysInMonth": 31,
            "dailyHours": {
                "1": "(7) | (-)",
                "2": "(7) | (-)"
            }
        }
    ]
    
    # Parameters for Google Apps Script GET request
    params = {
        "action": "sync_daily_grid",
        "data": json.dumps(test_data)
    }
    
    try:
        print("Sending direct request to Google Apps Script...")
        response = requests.get(
            GOOGLE_APPS_SCRIPT_URL,
            params=params,
            timeout=30
        )
        
        print(f"Response Status Code: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ SUCCESS: Google Apps Script responded successfully!")
            try:
                result = response.json()
                print("Response JSON:")
                print(json.dumps(result, indent=2))
                
                if result.get('status') == 'success':
                    print("\n🎉 GOOGLE APPS SCRIPT IS WORKING CORRECTLY!")
                else:
                    print(f"\n⚠️  Google Apps Script error: {result.get('message', 'Unknown error')}")
                    
            except json.JSONDecodeError:
                print("Response is not valid JSON:")
                print(response.text)
        else:
            print(f"❌ FAILED: Google Apps Script returned {response.status_code}")
            print("Response:")
            print(response.text)
            
    except Exception as e:
        print(f"❌ FAILED: Error calling Google Apps Script: {str(e)}")
        return False
    
    return response.status_code == 200

if __name__ == "__main__":
    print("Starting sync functionality tests...")
    print(f"Timestamp: {datetime.now().isoformat()}")
    
    # Test 1: Flask sync endpoint
    flask_success = test_sync_endpoint()
    
    # Test 2: Direct Google Apps Script
    script_success = test_direct_google_script()
    
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    print(f"Flask sync endpoint: {'✅ PASS' if flask_success else '❌ FAIL'}")
    print(f"Google Apps Script direct: {'✅ PASS' if script_success else '❌ FAIL'}")
    
    if flask_success and script_success:
        print("\n🎉 ALL TESTS PASSED!")
        print("The sync functionality fix is working correctly.")
        sys.exit(0)
    else:
        print("\n❌ SOME TESTS FAILED")
        print("Please check the error messages above for troubleshooting.")
        sys.exit(1)
