#!/usr/bin/env python3
"""
Staging System Test Script
Tests all staging functionality including API endpoints and database operations.
"""

import requests
import json
import time
from datetime import datetime, timedelta

# Test configuration
FLASK_APP_URL = "http://localhost:5173"
TEST_DATE_START = (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d')
TEST_DATE_END = datetime.now().strftime('%Y-%m-%d')
TEST_BUS_CODE = "PTRJ"

def print_test_header(test_name):
    """Print formatted test header"""
    print(f"\n{'='*60}")
    print(f"🧪 {test_name}")
    print(f"{'='*60}")

def print_test_result(test_name, success, details=""):
    """Print formatted test result"""
    status = "✅ PASSED" if success else "❌ FAILED"
    print(f"{status} - {test_name}")
    if details:
        print(f"   Details: {details}")

def test_flask_app_running():
    """Test if Flask application is running"""
    print_test_header("Flask Application Status")
    
    try:
        response = requests.get(f"{FLASK_APP_URL}/", timeout=10)
        success = response.status_code == 200
        print_test_result("Flask app accessibility", success, f"Status: {response.status_code}")
        return success
    except requests.exceptions.RequestException as e:
        print_test_result("Flask app accessibility", False, f"Error: {e}")
        return False

def test_staging_database_initialization():
    """Test staging database initialization"""
    print_test_header("Staging Database Initialization")
    
    try:
        # Test staging stats endpoint (this will trigger database initialization)
        response = requests.get(f"{FLASK_APP_URL}/api/staging/stats", timeout=10)
        success = response.status_code == 200
        
        if success:
            data = response.json()
            print_test_result("Staging database initialization", data.get('success', False), 
                            f"Response: {json.dumps(data, indent=2)}")
            return data.get('success', False)
        else:
            print_test_result("Staging database initialization", False, 
                            f"HTTP {response.status_code}: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print_test_result("Staging database initialization", False, f"Error: {e}")
        return False

def test_move_to_staging():
    """Test moving records to staging"""
    print_test_header("Move Records to Staging")
    
    try:
        move_data = {
            "start_date": TEST_DATE_START,
            "end_date": TEST_DATE_END,
            "bus_code": TEST_BUS_CODE
        }
        
        response = requests.post(
            f"{FLASK_APP_URL}/api/staging/move-to-staging",
            json=move_data,
            timeout=30
        )
        
        success = response.status_code == 200
        
        if success:
            data = response.json()
            moved_records = data.get('moved_records', 0)
            print_test_result("Move to staging", data.get('success', False), 
                            f"Moved {moved_records} records")
            return data.get('success', False), moved_records
        else:
            print_test_result("Move to staging", False, 
                            f"HTTP {response.status_code}: {response.text}")
            return False, 0
            
    except requests.exceptions.RequestException as e:
        print_test_result("Move to staging", False, f"Error: {e}")
        return False, 0

def test_staging_data_retrieval():
    """Test retrieving staging data"""
    print_test_header("Staging Data Retrieval")
    
    try:
        params = {
            "limit": 100,
            "offset": 0,
            "status": "staged"
        }
        
        response = requests.get(
            f"{FLASK_APP_URL}/api/staging/data",
            params=params,
            timeout=10
        )
        
        success = response.status_code == 200
        
        if success:
            data = response.json()
            records_count = len(data.get('data', []))
            print_test_result("Staging data retrieval", data.get('success', False), 
                            f"Retrieved {records_count} staging records")
            return data.get('success', False), data.get('data', [])
        else:
            print_test_result("Staging data retrieval", False, 
                            f"HTTP {response.status_code}: {response.text}")
            return False, []
            
    except requests.exceptions.RequestException as e:
        print_test_result("Staging data retrieval", False, f"Error: {e}")
        return False, []

def test_staging_record_update(staging_records):
    """Test updating a staging record"""
    print_test_header("Staging Record Update")
    
    if not staging_records:
        print_test_result("Staging record update", False, "No staging records available for testing")
        return False
    
    try:
        # Get first record for testing
        test_record = staging_records[0]
        record_id = test_record.get('id')
        
        if not record_id:
            print_test_result("Staging record update", False, "No record ID found")
            return False
        
        # Update data
        update_data = {
            "task_code": "TEST_TASK_001",
            "station_code": "TEST_STATION",
            "machine_code": "TEST_MACHINE",
            "expense_code": "TEST_EXPENSE"
        }
        
        response = requests.put(
            f"{FLASK_APP_URL}/api/staging/data/{record_id}",
            json=update_data,
            timeout=10
        )
        
        success = response.status_code == 200
        
        if success:
            data = response.json()
            print_test_result("Staging record update", data.get('success', False), 
                            f"Updated record ID: {record_id}")
            return data.get('success', False)
        else:
            print_test_result("Staging record update", False, 
                            f"HTTP {response.status_code}: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print_test_result("Staging record update", False, f"Error: {e}")
        return False

def test_staging_statistics():
    """Test staging statistics endpoint"""
    print_test_header("Staging Statistics")
    
    try:
        response = requests.get(f"{FLASK_APP_URL}/api/staging/stats", timeout=10)
        success = response.status_code == 200
        
        if success:
            data = response.json()
            stats = data.get('stats', {})
            total_records = stats.get('total_records', 0)
            status_counts = stats.get('status_counts', {})
            
            print_test_result("Staging statistics", data.get('success', False), 
                            f"Total: {total_records}, Status counts: {status_counts}")
            return data.get('success', False)
        else:
            print_test_result("Staging statistics", False, 
                            f"HTTP {response.status_code}: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print_test_result("Staging statistics", False, f"Error: {e}")
        return False

def test_staging_upload_preparation(staging_records):
    """Test preparing staging records for upload"""
    print_test_header("Staging Upload Preparation")
    
    if not staging_records:
        print_test_result("Staging upload preparation", False, "No staging records available for testing")
        return False
    
    try:
        # Prepare first record for upload
        test_record = staging_records[0]
        record_id = test_record.get('id')
        
        upload_data = {
            "record_ids": [record_id] if record_id else []
        }
        
        response = requests.post(
            f"{FLASK_APP_URL}/api/staging/upload",
            json=upload_data,
            timeout=10
        )
        
        success = response.status_code == 200
        
        if success:
            data = response.json()
            affected_records = data.get('affected_records', 0)
            print_test_result("Staging upload preparation", data.get('success', False), 
                            f"Prepared {affected_records} records for upload")
            return data.get('success', False)
        else:
            print_test_result("Staging upload preparation", False, 
                            f"HTTP {response.status_code}: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print_test_result("Staging upload preparation", False, f"Error: {e}")
        return False

def test_staging_record_deletion(staging_records):
    """Test deleting a staging record"""
    print_test_header("Staging Record Deletion")
    
    if len(staging_records) < 2:
        print_test_result("Staging record deletion", False, "Need at least 2 staging records for safe testing")
        return False
    
    try:
        # Delete the last record for testing
        test_record = staging_records[-1]
        record_id = test_record.get('id')
        
        if not record_id:
            print_test_result("Staging record deletion", False, "No record ID found")
            return False
        
        response = requests.delete(
            f"{FLASK_APP_URL}/api/staging/data/{record_id}",
            timeout=10
        )
        
        success = response.status_code == 200
        
        if success:
            data = response.json()
            print_test_result("Staging record deletion", data.get('success', False), 
                            f"Deleted record ID: {record_id}")
            return data.get('success', False)
        else:
            print_test_result("Staging record deletion", False, 
                            f"HTTP {response.status_code}: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print_test_result("Staging record deletion", False, f"Error: {e}")
        return False

def run_comprehensive_staging_tests():
    """Run all staging system tests"""
    print(f"\n🚀 Starting Comprehensive Staging System Tests")
    print(f"📅 Test Date Range: {TEST_DATE_START} to {TEST_DATE_END}")
    print(f"🏢 Business Code: {TEST_BUS_CODE}")
    print(f"🌐 Flask App URL: {FLASK_APP_URL}")
    
    test_results = []
    staging_records = []
    
    # Test 1: Flask app running
    result = test_flask_app_running()
    test_results.append(("Flask App Running", result))
    
    if not result:
        print("\n❌ Flask app is not running. Please start the application first.")
        return
    
    # Test 2: Database initialization
    result = test_staging_database_initialization()
    test_results.append(("Database Initialization", result))
    
    # Test 3: Move records to staging
    result, moved_count = test_move_to_staging()
    test_results.append(("Move to Staging", result))
    
    # Test 4: Retrieve staging data
    result, staging_records = test_staging_data_retrieval()
    test_results.append(("Data Retrieval", result))
    
    # Test 5: Update staging record
    if staging_records:
        result = test_staging_record_update(staging_records)
        test_results.append(("Record Update", result))
    
    # Test 6: Staging statistics
    result = test_staging_statistics()
    test_results.append(("Statistics", result))
    
    # Test 7: Upload preparation
    if staging_records:
        result = test_staging_upload_preparation(staging_records)
        test_results.append(("Upload Preparation", result))
    
    # Test 8: Record deletion (last to avoid affecting other tests)
    if staging_records:
        result = test_staging_record_deletion(staging_records)
        test_results.append(("Record Deletion", result))
    
    # Print summary
    print_test_header("Test Summary")
    passed_tests = sum(1 for _, result in test_results if result)
    total_tests = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{status} - {test_name}")
    
    print(f"\n📊 Overall Results: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        print("🎉 All staging system tests passed successfully!")
    else:
        print("⚠️  Some tests failed. Please check the implementation.")
    
    return passed_tests == total_tests

if __name__ == "__main__":
    try:
        success = run_comprehensive_staging_tests()
        exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⏹️  Tests interrupted by user")
        exit(1)
    except Exception as e:
        print(f"\n\n💥 Unexpected error during testing: {e}")
        exit(1) 