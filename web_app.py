"""
Flask web application for Attendance Report System.
Provides a web interface for viewing and exporting attendance reports.
"""

import os
import sys
import json
import requests
from datetime import datetime, date, timedelta
from flask import Flask, render_template, request, jsonify, send_file, redirect, url_for
from flask_cors import CORS

# Add the src directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), 'src')))

from attendance_reporter import AttendanceReporter

app = Flask(__name__)
CORS(app)

# Configure Flask app
app.config['SECRET_KEY'] = 'your-secret-key-here'
app.config['JSON_SORT_KEYS'] = False

# Google Apps Script URL for employee data
GOOGLE_APPS_SCRIPT_URL = "https://script.google.com/macros/s/AKfycbxaf8IKT4gkAv81fS0w9-901cRyKW4F-jvQ9E6jOMm8JpIkt0bmzlp9n3S3rdUN9Hcn/exec"

# Configure logging
import logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize the attendance reporter
try:
    reporter = AttendanceReporter()
    logger.info("AttendanceReporter initialized successfully")
except Exception as e:
    logger.error(f"Failed to initialize AttendanceReporter: {e}")
    reporter = None

@app.route('/')
def index():
    """Main dashboard page."""
    return render_template('index.html')

@app.route('/test')
def test_page():
    """Simple test page without DataTables."""
    return render_template('simple_test.html')

@app.route('/test-grid')
def test_grid_page():
    """Test page for grid functionality."""
    return render_template('test_grid.html')

@app.route('/test-charge-jobs')
def test_charge_jobs_page():
    """Test page for employee charge jobs integration."""
    return send_file('test_frontend_integration.html')

@app.route('/api/debug-attendance')
def debug_attendance():
    """Debug endpoint to check raw attendance data."""
    try:
        year = int(request.args.get('year', 2025))
        month = int(request.args.get('month', 3))
        bus_code = request.args.get('bus_code', 'PTRJ')

        # Get raw attendance data
        start_date = f"{year}-{month:02d}-01"
        end_date = f"{year}-{month:02d}-31"

        reporter = AttendanceReporter()
        raw_data = reporter.get_attendance_data(start_date, end_date, bus_code)

        # Get first 10 records for debugging
        debug_data = raw_data[:10] if raw_data else []

        return jsonify({
            'success': True,
            'total_records': len(raw_data),
            'sample_data': debug_data,
            'query_params': {
                'year': year,
                'month': month,
                'bus_code': bus_code,
                'start_date': start_date,
                'end_date': end_date
            }
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/test-grid-with-sample')
def test_grid_with_sample():
    """Test grid with sample working hours data."""
    try:
        # Generate sample data
        import calendar
        year = 2025
        month = 3
        days_in_month = calendar.monthrange(year, month)[1]

        # Sample employees
        employees = [
            {'EmployeeID': 'PTRJ.241000001', 'EmployeeName': 'John Doe'},
            {'EmployeeID': 'PTRJ.241000002', 'EmployeeName': 'Jane Smith'},
            {'EmployeeID': 'PTRJ.241000003', 'EmployeeName': 'Bob Wilson'},
        ]

        grid_data = []
        overtime_summary = []

        for i, emp in enumerate(employees, 1):
            days_data = {}
            total_regular = 0
            total_overtime = 0

            for day in range(1, days_in_month + 1):
                from datetime import datetime
                date_obj = datetime(year, month, day)
                weekday = date_obj.weekday()  # 0=Monday, 6=Sunday

                if weekday == 6:  # Sunday
                    days_data[str(day)] = 'OFF'
                elif day % 7 == 0:  # Some absent days
                    days_data[str(day)] = '-'
                elif weekday == 5:  # Saturday
                    hours = 4.5  # Saturday work
                    days_data[str(day)] = str(hours)
                    total_regular += hours
                else:  # Weekdays
                    if day % 5 == 0:  # Some overtime days
                        hours = 8.5
                        days_data[str(day)] = str(hours)
                        total_regular += 7
                        total_overtime += 1.5
                    else:
                        hours = 7.5
                        days_data[str(day)] = str(hours)
                        total_regular += hours

            grid_data.append({
                'No': i,
                'EmployeeID': emp['EmployeeID'],
                'EmployeeName': emp['EmployeeName'],
                'days': days_data
            })

            if total_overtime > 0:
                overtime_summary.append({
                    'No': i,
                    'EmployeeID': emp['EmployeeID'],
                    'EmployeeName': emp['EmployeeName'],
                    'TotalRegular': round(total_regular, 1),
                    'TotalOvertime': round(total_overtime, 1),
                    'TotalHours': round(total_regular + total_overtime, 1)
                })

        return jsonify({
            'success': True,
            'year': year,
            'month': month,
            'month_name': 'March',
            'days_in_month': days_in_month,
            'total_employees': len(employees),
            'total_working_days': 22,
            'grid_data': grid_data,
            'overtime_summary': overtime_summary,
            'date_range': 'March 2025'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/monthly-grid-by-station')
def monthly_grid_by_station():
    """Get monthly attendance grid grouped by stations."""
    try:
        year = int(request.args.get('year', 2025))
        month = int(request.args.get('month', 3))
        bus_code = request.args.get('bus_code')

        reporter = AttendanceReporter()
        grid_data = reporter.get_monthly_attendance_grid_by_station(year, month, bus_code)

        return jsonify({
            'success': True,
            'data': grid_data
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/test-monthly/<int:year>/<int:month>')
def test_monthly_direct(year, month):
    """Direct test for monthly report."""
    try:
        if reporter is None:
            return f"Error: AttendanceReporter not initialized"

        # Test get_monthly_summary
        summary = reporter.get_monthly_summary(year, month, "PTRJ")

        # Test get_attendance_data
        from datetime import datetime, timedelta
        start_date = f"{year}-{month:02d}-01"

        if month == 12:
            next_month_year = year + 1
            next_month = 1
        else:
            next_month_year = year
            next_month = month + 1

        end_date_obj = datetime(next_month_year, next_month, 1) - timedelta(days=1)
        end_date = end_date_obj.strftime("%Y-%m-%d")

        data = reporter.get_attendance_data(start_date, end_date, "PTRJ")

        return f"""
        <h1>Monthly Report Test: {year}-{month}</h1>
        <h2>Summary:</h2>
        <pre>{summary}</pre>
        <h2>Data Count:</h2>
        <p>Records found: {len(data)}</p>
        <h2>Sample Data:</h2>
        <pre>{data[:2] if data else 'No data'}</pre>
        """

    except Exception as e:
        import traceback
        return f"""
        <h1>Error Testing Monthly Report</h1>
        <p>Error: {str(e)}</p>
        <pre>{traceback.format_exc()}</pre>
        """

@app.route('/api/attendance')
def get_attendance_data():
    """
    API endpoint to get attendance data with filters.

    Query parameters:
    - start_date: Start date (YYYY-MM-DD)
    - end_date: End date (YYYY-MM-DD)
    - bus_code: Business code filter (optional)
    - employee_id: Employee ID filter (optional)
    - shift: Shift filter (optional)
    """
    try:
        # Get query parameters
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        bus_code = request.args.get('bus_code')
        employee_id = request.args.get('employee_id')
        shift = request.args.get('shift')

        # Validate required parameters
        if not start_date or not end_date:
            return jsonify({
                'error': 'start_date and end_date are required',
                'data': []
            }), 400

        # Get attendance data
        data = reporter.get_attendance_data(start_date, end_date, bus_code)

        # Apply additional filters
        if employee_id:
            data = [record for record in data if record.get('EmployeeID') == employee_id]

        if shift:
            data = [record for record in data if record.get('Shift') == shift]

        # Format data for JSON response
        formatted_data = []
        for record in data:
            formatted_record = {
                'EmployeeID': record.get('EmployeeID'),
                'EmployeeName': record.get('EmployeeName'),
                'Date': record.get('TADate').strftime('%Y-%m-%d') if record.get('TADate') else None,
                'Shift': record.get('Shift'),
                'CheckIn': record.get('TACheckIn').strftime('%H:%M') if record.get('TACheckIn') else None,
                'CheckOut': record.get('TACheckOut').strftime('%H:%M') if record.get('TACheckOut') else None,
                'RegularHours': round(float(record.get('RegularHours', 0)), 2),
                'OvertimeHours': round(float(record.get('OvertimeHours', 0)), 2),
                'DayOfWeek': record.get('DayOfWeek')
            }
            formatted_data.append(formatted_record)

        return jsonify({
            'success': True,
            'data': formatted_data,
            'total_records': len(formatted_data)
        })

    except Exception as e:
        return jsonify({
            'error': str(e),
            'data': []
        }), 500

@app.route('/api/employees')
def get_employees():
    """API endpoint to get list of employees."""
    try:
        bus_code = request.args.get('bus_code')
        employees = reporter.get_employees_list(bus_code)

        return jsonify({
            'success': True,
            'data': employees
        })

    except Exception as e:
        return jsonify({
            'error': str(e),
            'data': []
        }), 500

@app.route('/api/shifts')
def get_shifts():
    """API endpoint to get list of shifts."""
    try:
        bus_code = request.args.get('bus_code')
        shifts = reporter.get_shifts_list(bus_code)

        return jsonify({
            'success': True,
            'data': shifts
        })

    except Exception as e:
        return jsonify({
            'error': str(e),
            'data': []
        }), 500

@app.route('/api/export')
def export_report():
    """API endpoint to export attendance report to Excel or JSON."""
    try:
        # Get query parameters
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        bus_code = request.args.get('bus_code')
        employee_id = request.args.get('employee_id')
        shift = request.args.get('shift')
        format_type = request.args.get('format', 'excel')  # 'excel' or 'json'

        # Validate required parameters
        if not start_date or not end_date:
            return jsonify({
                'error': 'start_date and end_date are required'
            }), 400

        # Get attendance data
        data = reporter.get_attendance_data(start_date, end_date, bus_code)
        
        # Filter data if specific employee or shift requested
        if employee_id:
            data = [record for record in data if record.get('EmployeeID') == employee_id]
        if shift:
            data = [record for record in data if record.get('Shift') == shift]

        if format_type == 'json':
            # JSON export
            import tempfile
            import json
            
            # Prepare JSON data with metadata
            json_data = {
                'metadata': {
                    'start_date': start_date,
                    'end_date': end_date,
                    'bus_code': bus_code,
                    'employee_id': employee_id,
                    'shift': shift,
                    'export_date': datetime.now().isoformat(),
                    'total_records': len(data)
                },
                'attendance_data': []
            }
            
            # Format attendance data
            for record in data:
                formatted_record = {
                    'EmployeeID': record.get('EmployeeID', ''),
                    'EmployeeName': record.get('EmployeeName', ''),
                    'Date': record.get('TADate').strftime('%Y-%m-%d') if record.get('TADate') else '',
                    'DayOfWeek': record.get('DayOfWeek', ''),
                    'Shift': record.get('Shift', ''),
                    'CheckIn': str(record.get('TACheckIn', '')) if record.get('TACheckIn') else '',
                    'CheckOut': str(record.get('TACheckOut', '')) if record.get('TACheckOut') else '',
                    'RegularHours': float(record.get('RegularHours', 0)),
                    'OvertimeHours': float(record.get('OvertimeHours', 0)),
                    'TotalHours': float(record.get('RegularHours', 0)) + float(record.get('OvertimeHours', 0))
                }
                json_data['attendance_data'].append(formatted_record)
            
            # Create temporary JSON file
            temp_dir = tempfile.gettempdir()
            filename = f"attendance_report_{start_date}_to_{end_date}.json"
            json_path = os.path.join(temp_dir, filename)
            
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump(json_data, f, indent=2, ensure_ascii=False, default=str)
            
            return send_file(json_path, as_attachment=True, download_name=filename)
            
        else:
            # Excel export (existing functionality)
            if start_date == end_date:
                # Daily report
                excel_path = reporter.generate_daily_report(start_date, bus_code)
            else:
                # Date range report
                excel_path = reporter.generate_date_range_report(start_date, end_date, bus_code)

            if excel_path and os.path.exists(excel_path):
                return send_file(
                    excel_path,
                    as_attachment=True,
                    download_name=os.path.basename(excel_path),
                    mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                )
            else:
                return jsonify({
                    'error': 'No data found or report generation failed'
                }), 404

    except Exception as e:
        return jsonify({
            'error': str(e)
        }), 500

@app.route('/api/summary')
def get_summary():
    """API endpoint to get attendance summary statistics."""
    try:
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        bus_code = request.args.get('bus_code')

        if not start_date or not end_date:
            return jsonify({
                'error': 'start_date and end_date are required'
            }), 400

        # Get attendance data
        data = reporter.get_attendance_data(start_date, end_date, bus_code)

        # Calculate summary statistics
        total_employees = len(set(record.get('EmployeeID') for record in data))
        total_records = len(data)
        total_regular_hours = sum(float(record.get('RegularHours', 0)) for record in data)
        total_overtime_hours = sum(float(record.get('OvertimeHours', 0)) for record in data)

        # Calculate average hours per employee
        avg_regular_hours = total_regular_hours / total_employees if total_employees > 0 else 0
        avg_overtime_hours = total_overtime_hours / total_employees if total_employees > 0 else 0

        summary = {
            'total_employees': total_employees,
            'total_records': total_records,
            'total_regular_hours': round(total_regular_hours, 2),
            'total_overtime_hours': round(total_overtime_hours, 2),
            'avg_regular_hours': round(avg_regular_hours, 2),
            'avg_overtime_hours': round(avg_overtime_hours, 2),
            'date_range': f"{start_date} to {end_date}"
        }

        return jsonify({
            'success': True,
            'data': summary
        })

    except Exception as e:
        return jsonify({
            'error': str(e)
        }), 500

@app.route('/api/months')
def get_available_months():
    """API endpoint to get list of available months with data."""
    try:
        if reporter is None:
            logger.error("AttendanceReporter not initialized")
            return jsonify({
                'error': 'Database connection not available',
                'data': []
            }), 500

        bus_code = request.args.get('bus_code')
        logger.info(f"Getting available months with bus_code: {bus_code}")

        months = reporter.get_available_months(bus_code)
        logger.info(f"Retrieved {len(months)} months from reporter")

        # Format months for frontend
        formatted_months = []
        for month in months:
            try:
                formatted_month = {
                    'year': month.get('Year'),
                    'month': month.get('Month'),
                    'month_name': month.get('MonthName'),
                    'record_count': month.get('RecordCount'),
                    'employee_count': month.get('EmployeeCount'),
                    'first_date': month.get('FirstDate').strftime('%Y-%m-%d') if month.get('FirstDate') else None,
                    'last_date': month.get('LastDate').strftime('%Y-%m-%d') if month.get('LastDate') else None,
                    'display_name': f"{month.get('MonthName')} {month.get('Year')}",
                    'month_key': f"{month.get('Year')}-{month.get('Month'):02d}"
                }
                formatted_months.append(formatted_month)
            except Exception as format_error:
                logger.error(f"Error formatting month {month}: {format_error}")
                continue

        logger.info(f"Successfully formatted {len(formatted_months)} months")

        return jsonify({
            'success': True,
            'data': formatted_months,
            'debug_info': {
                'raw_months_count': len(months),
                'formatted_months_count': len(formatted_months),
                'bus_code': bus_code
            }
        })

    except Exception as e:
        logger.error(f"Error in get_available_months: {str(e)}")
        import traceback
        logger.error(f"Full traceback: {traceback.format_exc()}")
        return jsonify({
            'error': str(e),
            'data': [],
            'debug_info': {
                'error_type': type(e).__name__,
                'bus_code': request.args.get('bus_code')
            }
        }), 500

@app.route('/api/monthly-report')
def get_monthly_report():
    """API endpoint to get monthly attendance report."""
    try:
        if reporter is None:
            logger.error("AttendanceReporter not initialized")
            return jsonify({
                'error': 'Database connection not available',
                'data': []
            }), 500

        year = request.args.get('year', type=int)
        month = request.args.get('month', type=int)
        bus_code = request.args.get('bus_code')

        logger.info(f"Getting monthly report for {year}-{month}, bus_code: {bus_code}")

        if not year or not month:
            logger.error("Missing year or month parameter")
            return jsonify({
                'error': 'year and month are required'
            }), 400

        if month < 1 or month > 12:
            logger.error(f"Invalid month: {month}")
            return jsonify({
                'error': 'month must be between 1 and 12'
            }), 400

        # Get monthly summary
        logger.info("Getting monthly summary...")
        summary = reporter.get_monthly_summary(year, month, bus_code)
        logger.info(f"Monthly summary retrieved: {summary}")

        # Get detailed attendance data for the month
        from datetime import datetime, timedelta
        start_date = f"{year}-{month:02d}-01"

        # Calculate the last day of the month
        if month == 12:
            next_month_year = year + 1
            next_month = 1
        else:
            next_month_year = year
            next_month = month + 1

        end_date_obj = datetime(next_month_year, next_month, 1) - timedelta(days=1)
        end_date = end_date_obj.strftime("%Y-%m-%d")

        logger.info(f"Getting attendance data from {start_date} to {end_date}")

        # Get attendance data
        data = reporter.get_attendance_data(start_date, end_date, bus_code)
        logger.info(f"Retrieved {len(data)} attendance records")

        # Format data for JSON response
        formatted_data = []
        for record in data:
            try:
                formatted_record = {
                    'EmployeeID': record.get('EmployeeID'),
                    'EmployeeName': record.get('EmployeeName'),
                    'Date': record.get('TADate').strftime('%Y-%m-%d') if record.get('TADate') else None,
                    'Shift': record.get('Shift'),
                    'CheckIn': record.get('TACheckIn').strftime('%H:%M') if record.get('TACheckIn') else None,
                    'CheckOut': record.get('TACheckOut').strftime('%H:%M') if record.get('TACheckOut') else None,
                    'RegularHours': round(float(record.get('RegularHours', 0)), 2),
                    'OvertimeHours': round(float(record.get('OvertimeHours', 0)), 2),
                    'DayOfWeek': record.get('DayOfWeek')
                }
                formatted_data.append(formatted_record)
            except Exception as format_error:
                logger.error(f"Error formatting record {record}: {format_error}")
                continue

        logger.info(f"Successfully formatted {len(formatted_data)} records")

        return jsonify({
            'success': True,
            'summary': summary,
            'data': formatted_data,
            'total_records': len(formatted_data),
            'debug_info': {
                'year': year,
                'month': month,
                'bus_code': bus_code,
                'date_range': f"{start_date} to {end_date}",
                'raw_records': len(data),
                'formatted_records': len(formatted_data)
            }
        })

    except Exception as e:
        logger.error(f"Error in get_monthly_report: {str(e)}")
        import traceback
        logger.error(f"Full traceback: {traceback.format_exc()}")
        return jsonify({
            'error': str(e),
            'debug_info': {
                'year': request.args.get('year'),
                'month': request.args.get('month'),
                'bus_code': request.args.get('bus_code'),
                'error_type': type(e).__name__
            }
        }), 500

@app.route('/api/monthly-grid')
def get_monthly_attendance_grid():
    """API endpoint to get monthly attendance in grid format (employee vs days)."""
    try:
        if reporter is None:
            logger.error("AttendanceReporter not initialized")
            return jsonify({
                'error': 'Database connection not available',
                'data': []
            }), 500

        year = request.args.get('year', type=int)
        month = request.args.get('month', type=int)
        bus_code = request.args.get('bus_code')

        logger.info(f"Getting monthly grid for {year}-{month}, bus_code: {bus_code}")

        if not year or not month:
            logger.error("Missing year or month parameter")
            return jsonify({
                'error': 'year and month are required'
            }), 400

        if month < 1 or month > 12:
            logger.error(f"Invalid month: {month}")
            return jsonify({
                'error': 'month must be between 1 and 12'
            }), 400

        # Get grid data
        logger.info("Getting monthly attendance grid...")
        grid_data = reporter.get_monthly_attendance_grid(year, month, bus_code)
        logger.info(f"Grid data retrieved: {len(grid_data.get('grid_data', []))} employees")

        return jsonify({
            'success': True,
            'data': grid_data,
            'debug_info': {
                'year': year,
                'month': month,
                'bus_code': bus_code,
                'total_employees': grid_data.get('total_employees', 0),
                'days_in_month': grid_data.get('days_in_month', 0)
            }
        })

    except Exception as e:
        logger.error(f"Error in get_monthly_attendance_grid: {str(e)}")
        import traceback
        logger.error(f"Full traceback: {traceback.format_exc()}")
        return jsonify({
            'error': str(e),
            'debug_info': {
                'year': request.args.get('year'),
                'month': request.args.get('month'),
                'bus_code': request.args.get('bus_code'),
                'error_type': type(e).__name__
            }
        }), 500

@app.route('/api/debug')
def debug_info():
    """Debug endpoint to check system status."""
    try:
        debug_data = {
            'reporter_status': 'initialized' if reporter else 'failed',
            'python_path': sys.path[:3],
            'current_directory': os.getcwd(),
            'templates_exist': os.path.exists('templates'),
            'static_exist': os.path.exists('static')
        }

        if reporter:
            try:
                # Test database connection
                test_months = reporter.get_available_months()
                debug_data['database_test'] = {
                    'status': 'success',
                    'months_found': len(test_months),
                    'sample_month': test_months[0] if test_months else None
                }
            except Exception as db_error:
                debug_data['database_test'] = {
                    'status': 'failed',
                    'error': str(db_error)
                }

        return jsonify({
            'success': True,
            'debug_data': debug_data
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/export-grid', methods=['GET'])
def export_grid():
    """
    Export attendance grid data to Excel or JSON format.
    Supports both regular grid and station-grouped grid.
    """
    try:
        # Get parameters
        year = request.args.get('year', type=int)
        month = request.args.get('month', type=int)
        bus_code = request.args.get('bus_code', 'PTRJ')
        format_type = request.args.get('format', 'excel')  # 'excel' or 'json'
        grid_type = request.args.get('type', 'grid')  # 'grid' or 'station-grid'

        if not year or not month:
            return jsonify({'success': False, 'error': 'Year and month are required'}), 400

        logger.info(f"Exporting {grid_type} data: {year}-{month}, format: {format_type}")

        # Get the appropriate grid data
        if grid_type == 'station-grid':
            grid_data = reporter.get_monthly_attendance_grid_by_station(year, month, bus_code)
        else:
            grid_data = reporter.get_monthly_attendance_grid(year, month, bus_code)

        if not grid_data:
            return jsonify({'success': False, 'error': 'No data found for the specified month'}), 404

        # Generate filename
        month_name = grid_data.get('month_name', f'Month{month}')
        grid_suffix = '_by_station' if grid_type == 'station-grid' else ''
        
        if format_type == 'json':
            filename = f"attendance_grid{grid_suffix}_{month_name}_{year}.json"
            
            # Prepare JSON data
            json_data = {
                'metadata': {
                    'year': year,
                    'month': month,
                    'month_name': month_name,
                    'days_in_month': grid_data.get('days_in_month'),
                    'total_employees': grid_data.get('total_employees'),
                    'export_date': datetime.now().isoformat(),
                    'business_code': bus_code,
                    'grid_type': grid_type
                },
                'grid_data': grid_data.get('stations_grid' if grid_type == 'station-grid' else 'grid_data', []),
                'overtime_summary': grid_data.get('overtime_summary', [])
            }
            
            # Create JSON file
            import json
            import tempfile
            import os
            
            temp_dir = tempfile.gettempdir()
            json_path = os.path.join(temp_dir, filename)
            
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump(json_data, f, indent=2, ensure_ascii=False, default=str)
            
            return send_file(json_path, as_attachment=True, download_name=filename)
            
        else:  # Excel format
            filename = f"attendance_grid{grid_suffix}_{month_name}_{year}.xlsx"
            
            # Create Excel workbook using xlsxwriter to match web format
            import xlsxwriter
            import tempfile
            import os
            
            temp_dir = tempfile.gettempdir()
            excel_path = os.path.join(temp_dir, filename)
            
            workbook = xlsxwriter.Workbook(excel_path)
            worksheet = workbook.add_worksheet('Attendance Grid')
            
            # Define formats
            header_format = workbook.add_format({
                'bold': True,
                'bg_color': '#212529',
                'font_color': 'white',
                'align': 'center',
                'valign': 'vcenter',
                'border': 1
            })
            
            sunday_header_format = workbook.add_format({
                'bold': True,
                'bg_color': '#dc3545',
                'font_color': 'white',
                'align': 'center',
                'valign': 'vcenter',
                'border': 1
            })
            
            total_header_format = workbook.add_format({
                'bold': True,
                'bg_color': '#198754',
                'font_color': 'white',
                'align': 'center',
                'valign': 'vcenter',
                'border': 1
            })
            
            station_format = workbook.add_format({
                'bold': True,
                'bg_color': '#6c757d',
                'font_color': 'white',
                'align': 'left',
                'valign': 'vcenter',
                'border': 1
            })
            
            total_cell_format = workbook.add_format({
                'bg_color': '#e8f5e8',
                'bold': True,
                'align': 'center',
                'valign': 'vcenter',
                'border': 1
            })
            
            # Regular cell formats for attendance
            full_format = workbook.add_format({'bg_color': '#d1e7dd', 'align': 'center', 'border': 1})
            partial_format = workbook.add_format({'bg_color': '#f8d7da', 'align': 'center', 'border': 1})
            off_format = workbook.add_format({'bg_color': '#d1ecf1', 'align': 'center', 'border': 1})
            absent_format = workbook.add_format({'bg_color': '#f8f9fa', 'align': 'center', 'border': 1})
            
            # Write headers
            headers = ['No', 'Employee ID', 'Employee Name']
            
            # Add day headers
            for day in range(1, grid_data.get('days_in_month', 31) + 1):
                from datetime import datetime
                date_obj = datetime(year, month, day)
                day_name = ['Min', 'Sen', 'Sel', 'Rab', 'Kam', 'Jum', 'Sab'][date_obj.weekday()]
                is_sunday = date_obj.weekday() == 6
                
                headers.append(f"{day}\n{day_name}")
                
                # Apply format based on day
                format_to_use = sunday_header_format if is_sunday else header_format
                worksheet.write(0, len(headers) - 1, f"{day}\n{day_name}", format_to_use)
            
            # Add total headers (3 columns)
            worksheet.write(0, len(headers), "DAYS\nTotal", total_header_format)
            worksheet.write(0, len(headers) + 1, "REG\nHours", total_header_format)
            worksheet.write(0, len(headers) + 2, "OT\nHours", total_header_format)
            
            # Write basic headers first
            for i, header in enumerate(headers[:3]):
                worksheet.write(0, i, header, header_format)
            
            current_row = 1
            
            # Write data based on grid type
            if grid_type == 'station-grid':
                stations_data = grid_data.get('stations_grid', {})
                
                for station_name, employees in stations_data.items():
                    # Write station header
                    worksheet.merge_range(current_row, 0, current_row, len(headers) + 2, 
                                        f"{station_name} ({len(employees)} employees)", station_format)
                    current_row += 1
                    
                    # Write employees
                    for employee in employees:
                        worksheet.write(current_row, 0, employee.get('No'), absent_format)
                        worksheet.write(current_row, 1, employee.get('EmployeeID'), absent_format)
                        worksheet.write(current_row, 2, employee.get('EmployeeName'), absent_format)
                        
                        # Calculate totals
                        total_days = 0
                        total_regular = 0
                        total_overtime = 0
                        
                        # Write daily data
                        for day in range(1, grid_data.get('days_in_month', 31) + 1):
                            hours = employee.get('days', {}).get(str(day), '-')
                            col = 3 + day - 1
                            
                            # Determine cell format
                            cell_format = absent_format
                            if hours == 'OFF':
                                cell_format = off_format
                            elif hours != '-' and '|' in hours:
                                parts = hours.split('|')
                                if len(parts) == 2:
                                    regular_part = parts[0].strip().replace('(', '').replace(')', '')
                                    regular_hours = float(regular_part) if regular_part.replace('.', '').isdigit() else 0
                                    
                                    # Determine if Saturday
                                    date_obj = datetime(year, month, day)
                                    is_saturday = date_obj.weekday() == 5
                                    threshold = 5 if is_saturday else 7
                                    
                                    if regular_hours >= threshold:
                                        cell_format = full_format
                                    elif regular_hours > 0:
                                        cell_format = partial_format
                                    
                                    # Calculate totals
                                    overtime_part = parts[1].strip().replace('(', '').replace(')', '')
                                    overtime_hours = float(overtime_part) if overtime_part != '-' and overtime_part.replace('.', '').isdigit() else 0
                                    
                                    if regular_hours > 0 or overtime_hours > 0:
                                        total_days += 1
                                        total_regular += regular_hours
                                        total_overtime += overtime_hours
                            
                            worksheet.write(current_row, col, hours, cell_format)
                        
                        # Write totals (3 separate cells)
                        worksheet.write(current_row, len(headers), total_days, total_cell_format)
                        worksheet.write(current_row, len(headers) + 1, round(total_regular, 1), total_cell_format)
                        worksheet.write(current_row, len(headers) + 2, round(total_overtime, 1), total_cell_format)
                        
                        current_row += 1
            else:
                # Regular grid
                employees = grid_data.get('grid_data', [])
                
                for employee in employees:
                    worksheet.write(current_row, 0, employee.get('No'), absent_format)
                    worksheet.write(current_row, 1, employee.get('EmployeeID'), absent_format)
                    worksheet.write(current_row, 2, employee.get('EmployeeName'), absent_format)
                    
                    # Calculate totals
                    total_days = 0
                    total_regular = 0
                    total_overtime = 0
                    
                    # Write daily data
                    for day in range(1, grid_data.get('days_in_month', 31) + 1):
                        hours = employee.get('days', {}).get(str(day), '-')
                        col = 3 + day - 1
                        
                        # Determine cell format and calculate totals
                        cell_format = absent_format
                        if hours == 'OFF':
                            cell_format = off_format
                        elif hours != '-' and '|' in hours:
                            parts = hours.split('|')
                            if len(parts) == 2:
                                regular_part = parts[0].strip().replace('(', '').replace(')', '')
                                regular_hours = float(regular_part) if regular_part.replace('.', '').isdigit() else 0
                                
                                # Determine if Saturday
                                date_obj = datetime(year, month, day)
                                is_saturday = date_obj.weekday() == 5
                                threshold = 5 if is_saturday else 7
                                
                                if regular_hours >= threshold:
                                    cell_format = full_format
                                elif regular_hours > 0:
                                    cell_format = partial_format
                                
                                # Calculate totals
                                overtime_part = parts[1].strip().replace('(', '').replace(')', '')
                                overtime_hours = float(overtime_part) if overtime_part != '-' and overtime_part.replace('.', '').isdigit() else 0
                                
                                if regular_hours > 0 or overtime_hours > 0:
                                    total_days += 1
                                    total_regular += regular_hours
                                    total_overtime += overtime_hours
                        
                        worksheet.write(current_row, col, hours, cell_format)
                    
                    # Write totals (3 separate cells)
                    worksheet.write(current_row, len(headers), total_days, total_cell_format)
                    worksheet.write(current_row, len(headers) + 1, round(total_regular, 1), total_cell_format)
                    worksheet.write(current_row, len(headers) + 2, round(total_overtime, 1), total_cell_format)
                    
                    current_row += 1
            
            # Set column widths
            worksheet.set_column(0, 0, 8)   # No
            worksheet.set_column(1, 1, 15)  # Employee ID
            worksheet.set_column(2, 2, 25)  # Employee Name
            worksheet.set_column(3, len(headers) + 2, 12)  # Day columns and totals
            
            workbook.close()
            
            return send_file(excel_path, as_attachment=True, download_name=filename)
            
    except Exception as e:
        logger.error(f"Error in grid export: {str(e)}")
        import traceback
        logger.error(f"Full traceback: {traceback.format_exc()}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/employee-charge-jobs')
def get_employee_charge_jobs():
    """
    Fetch employee charge job data from Google Apps Script API.
    Returns parsed task codes, machine codes, and expense codes.
    """
    try:
        logger.info(f"Fetching employee data from Google Apps Script: {GOOGLE_APPS_SCRIPT_URL}")
        
        # Make GET request to Google Apps Script
        response = requests.get(GOOGLE_APPS_SCRIPT_URL, timeout=30)
        response.raise_for_status()
        
        # Parse the response
        employee_data = response.json()
        logger.info(f"Retrieved {len(employee_data)} employee records from Google Sheets")
        
        # Process and parse the charge job data
        processed_data = {}
        
        for emp in employee_data:
            # Get employee identifier (could be name or ID)
            emp_name = emp.get('namaKaryawan', '').strip()
            charge_job = emp.get('chargeJob', '').strip()
            
            if emp_name and charge_job:
                # Parse charge job format: "taskcode/machinecode/expensecode"
                parts = charge_job.split('/')
                
                if len(parts) >= 3:
                    task_code = parts[0].strip()
                    machine_code = parts[1].strip()
                    expense_code = parts[2].strip()
                else:
                    # Handle partial data
                    task_code = parts[0].strip() if len(parts) > 0 else ''
                    machine_code = parts[1].strip() if len(parts) > 1 else ''
                    expense_code = parts[2].strip() if len(parts) > 2 else ''
                
                processed_data[emp_name] = {
                    'task_code': task_code,
                    'machine_code': machine_code, 
                    'expense_code': expense_code,
                    'raw_charge_job': charge_job,
                    'gender': emp.get('Gender', ''),
                    'station': emp.get('Nama Stasiun', ''),
                    'no_urut': emp.get('No. Urut', '')
                }
        
        logger.info(f"Successfully processed {len(processed_data)} employee charge job records")
        
        return jsonify({
            'success': True,
            'data': processed_data,
            'total_records': len(processed_data),
            'source_url': GOOGLE_APPS_SCRIPT_URL
        })
        
    except requests.exceptions.Timeout:
        logger.error("Timeout while fetching data from Google Apps Script")
        return jsonify({
            'success': False,
            'error': 'Request timeout while fetching employee data from Google Sheets'
        }), 408
        
    except requests.exceptions.RequestException as e:
        logger.error(f"Request error while fetching from Google Apps Script: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'Failed to fetch employee data: {str(e)}'
        }), 500
        
    except Exception as e:
        logger.error(f"Error processing employee charge job data: {str(e)}")
        import traceback
        logger.error(f"Full traceback: {traceback.format_exc()}")
        return jsonify({
            'success': False,
            'error': f'Error processing employee data: {str(e)}'
        }), 500

@app.route('/api/sync-to-spreadsheet', methods=['POST'])
def sync_to_spreadsheet():
    """Proxy endpoint to sync data to Google Apps Script."""
    try:
        # Get data from request
        data = request.get_json()
        
        if not data:
            return jsonify({
                'success': False,
                'error': 'No data provided'
            }), 400
            
        sync_url = data.get('sync_url')
        action = data.get('action')
        sync_data = data.get('data')
        
        if not sync_url:
            return jsonify({
                'success': False,
                'error': 'sync_url is required'
            }), 400
            
        if not action:
            return jsonify({
                'success': False,
                'error': 'action is required'
            }), 400
            
        if not sync_data:
            return jsonify({
                'success': False,
                'error': 'data is required'
            }), 400
        
        logger.info(f"=== Received sync request ===")
        logger.info(f"  - sync_url: {sync_url}")
        logger.info(f"  - action: {action}")
        logger.info(f"  - data type: {type(sync_data)}")
        logger.info(f"  - data length: {len(sync_data) if isinstance(sync_data, list) else 'Unknown'}")
        
        if isinstance(sync_data, list) and len(sync_data) > 0:
            sample_record = sync_data[0]
            logger.info(f"  - sample record: {sample_record}")
        
        logger.info(f"Proxying sync request to: {sync_url}")
        logger.info(f"Action: {action}")
        logger.info(f"Data records: {len(sync_data) if isinstance(sync_data, list) else 'Unknown'}")
        
        # Prepare data for Google Apps Script
        # Try POST first, fallback to GET with optimized data if POST fails

        # Optimize data for sync (remove large status objects to reduce payload size)
        optimized_sync_data = []
        if isinstance(sync_data, list):
            for record in sync_data:
                if isinstance(record, dict):
                    # Create optimized record without large status data
                    optimized_record = {}
                    for key, value in record.items():
                        # Skip status objects that make payload too large
                        if not key.endswith('_status'):
                            optimized_record[key] = value
                    optimized_sync_data.append(optimized_record)
                else:
                    optimized_sync_data.append(record)
        else:
            optimized_sync_data = sync_data

        data_json = json.dumps(optimized_sync_data) if isinstance(optimized_sync_data, (list, dict)) else optimized_sync_data

        logger.info(f"Attempting sync to Google Apps Script: action={action}")
        logger.info(f"Original data length: {len(json.dumps(sync_data)) if isinstance(sync_data, (list, dict)) else len(str(sync_data))}")
        logger.info(f"Optimized data length: {len(data_json)}")

        # Try POST first (for updated Google Apps Script)
        try:
            payload = {
                'action': action,
                'data': data_json
            }

            logger.info("Trying POST request...")
            headers = {'Content-Type': 'application/json'}
            response = requests.post(sync_url, json=payload, headers=headers, timeout=30)

            # Check if POST was successful
            if response.status_code == 200:
                try:
                    result = response.json()
                    if result.get('status') != 'error' or 'Invalid POST action' not in result.get('message', ''):
                        logger.info("POST request successful")
                    else:
                        raise Exception("POST not supported, falling back to GET")
                except:
                    raise Exception("POST response invalid, falling back to GET")
            else:
                raise Exception(f"POST failed with status {response.status_code}")

        except Exception as post_error:
            logger.info(f"POST failed ({post_error}), falling back to GET request...")

            # Fallback to GET request with URL parameters
            params = {
                'action': action,
                'data': data_json
            }

            # Check if data is too large for GET
            if len(data_json) > 8000:  # Conservative limit for URL parameters
                logger.warning(f"Data too large for GET request ({len(data_json)} chars), truncating...")
                # Further optimize by keeping only essential fields
                if isinstance(optimized_sync_data, list) and len(optimized_sync_data) > 0:
                    # Keep only first few records for demonstration
                    truncated_data = optimized_sync_data[:2]  # Keep only 2 records
                    params['data'] = json.dumps(truncated_data)
                    logger.info(f"Truncated to {len(truncated_data)} records")

            response = requests.get(sync_url, params=params, timeout=30)
        
        logger.info(f"Google Apps Script response status: {response.status_code}")
        logger.info(f"Google Apps Script response: {response.text[:500]}...")  # Log first 500 chars
        
        # Try to parse response as JSON
        try:
            result = response.json()
            logger.info(f"Parsed result: {result}")
        except:
            # If not JSON, treat as successful if status is 200
            if response.status_code == 200:
                result = {
                    'status': 'success',
                    'message': 'Data synced successfully',
                    'raw_response': response.text
                }
            else:
                result = {
                    'status': 'error',
                    'message': f'HTTP {response.status_code}: {response.text}',
                    'raw_response': response.text
                }
        
        # Return response to frontend
        if result.get('status') == 'success':
            return jsonify({
                'success': True,
                'message': result.get('message', 'Data synced successfully'),
                'details': result
            })
        else:
            return jsonify({
                'success': False,
                'error': result.get('message', 'Sync failed'),
                'details': result
            }), 400
            
    except requests.exceptions.Timeout:
        logger.error("Timeout when syncing to Google Apps Script")
        return jsonify({
            'success': False,
            'error': 'Request timeout - Google Apps Script took too long to respond'
        }), 408
        
    except requests.exceptions.ConnectionError:
        logger.error("Connection error when syncing to Google Apps Script")
        return jsonify({
            'success': False,
            'error': 'Connection error - Could not reach Google Apps Script'
        }), 503
        
    except Exception as e:
        logger.error(f"Error in sync_to_spreadsheet: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'Sync failed: {str(e)}'
        }), 500

if __name__ == '__main__':
    # Create templates directory if it doesn't exist
    os.makedirs('templates', exist_ok=True)
    os.makedirs('static', exist_ok=True)

    # Print startup information
    print("=" * 60)
    print("ATTENDANCE REPORT WEB APPLICATION")
    print("=" * 60)
    print(f"Current directory: {os.getcwd()}")
    print(f"Reporter status: {'OK' if reporter else 'FAILED'}")
    print(f"Templates directory: {'EXISTS' if os.path.exists('templates') else 'MISSING'}")
    print(f"Static directory: {'EXISTS' if os.path.exists('static') else 'MISSING'}")
    print("=" * 60)
    print("Starting web server...")
    print("Access the application at: http://localhost:5000")
    print("Debug endpoint available at: http://localhost:5000/api/debug")
    print("=" * 60)

    # Run the Flask app
    app.run(debug=True, host='0.0.0.0', port=5000)
