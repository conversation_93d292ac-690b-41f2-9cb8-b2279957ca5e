"""
Flask web application for Attendance Report System.
Provides a web interface for viewing and exporting attendance reports.
"""

import os
import sys
import json
import requests
from datetime import datetime, date, timedelta
from flask import Flask, render_template, request, jsonify, send_file, redirect, url_for
from flask_cors import CORS
import sqlite3
import uuid

# Add the src directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), 'src')))

from attendance_reporter import AttendanceReporter

app = Flask(__name__)
CORS(app)

# Configure Flask app
app.config['SECRET_KEY'] = 'your-secret-key-here'
app.config['JSON_SORT_KEYS'] = False

# Load configuration from config.json
def load_config():
    """Load configuration from config.json file."""
    config_path = os.path.join(os.path.dirname(__file__), 'config.json')
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        logger.error("config.json file not found. Using default configuration.")
        return {
            "google_apps_script": {
                "sync_url": "https://script.google.com/macros/s/AKfycbxaf8IKT4gkAv81fS0w9-901cRyKW4F-jvQ9E6jOMm8JpIkt0bmzlp9n3S3rdUN9Hcn/exec",
                "charge_job_data_url": "https://script.google.com/macros/s/AKfycbxy72FcKPhhuTJ3qT_DhJCLI8Z_xk9NmQlZ4mdmmtdZ-HDTHM8ER2RpYk40W--rmKjQ/exec"
            }
        }
    except json.JSONDecodeError as e:
        logger.error(f"Error parsing config.json: {e}. Using default configuration.")
        return {
            "google_apps_script": {
                "sync_url": "https://script.google.com/macros/s/AKfycbxaf8IKT4gkAv81fS0w9-901cRyKW4F-jvQ9E6jOMm8JpIkt0bmzlp9n3S3rdUN9Hcn/exec",
                "charge_job_data_url": "https://script.google.com/macros/s/AKfycbxy72FcKPhhuTJ3qT_DhJCLI8Z_xk9NmQlZ4mdmmtdZ-HDTHM8ER2RpYk40W--rmKjQ/exec"
            }
        }

# Configure logging
import logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Load configuration
config = load_config()

# Google Apps Script URLs from config
GOOGLE_APPS_SCRIPT_URL = config["google_apps_script"]["sync_url"]
CHARGE_JOB_DATA_URL = config["google_apps_script"]["charge_job_data_url"]

logger.info(f"Loaded configuration:")
logger.info(f"  - Sync URL: {GOOGLE_APPS_SCRIPT_URL}")
logger.info(f"  - Charge Job Data URL: {CHARGE_JOB_DATA_URL}")

# Add staging configuration
STAGING_CONFIG = config.get("staging_config", {
    "database_table": "staging_attendance",
    "max_records": 10000,
    "auto_cleanup_days": 30,
    "default_mode": "Local Sync Staging"
})

def init_staging_database():
    """Initialize the staging database table if it doesn't exist."""
    try:
        # Use the same database as the main application but separate table
        staging_db_path = os.path.join('data', 'staging_attendance.db')
        os.makedirs('data', exist_ok=True)
        
        conn = sqlite3.connect(staging_db_path)
        cursor = conn.cursor()
        
        # Create staging table with the same structure as main attendance data
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS staging_attendance (
                id TEXT PRIMARY KEY,
                employee_id TEXT NOT NULL,
                employee_name TEXT NOT NULL,
                date TEXT NOT NULL,
                day_of_week TEXT,
                shift TEXT,
                check_in TEXT,
                check_out TEXT,
                regular_hours REAL DEFAULT 0,
                overtime_hours REAL DEFAULT 0,
                total_hours REAL DEFAULT 0,
                task_code TEXT,
                station_code TEXT,
                machine_code TEXT,
                expense_code TEXT,
                status TEXT DEFAULT 'staged',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                source_record_id TEXT,
                notes TEXT
            )
        ''')
        
        # Create indexes for better performance
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_staging_employee_id ON staging_attendance(employee_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_staging_date ON staging_attendance(date)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_staging_status ON staging_attendance(status)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_staging_created_at ON staging_attendance(created_at)')
        
        conn.commit()
        conn.close()
        
        logger.info("Staging database initialized successfully")
        return True
        
    except Exception as e:
        logger.error(f"Failed to initialize staging database: {e}")
        return False

def get_staging_db_connection():
    """Get a connection to the staging database."""
    staging_db_path = os.path.join('data', 'staging_attendance.db')
    return sqlite3.connect(staging_db_path)

# Initialize staging database on startup
init_staging_database()

# Staging API Endpoints

@app.route('/api/staging/data', methods=['GET'])
def get_staging_data():
    """Get staging data for display in the grid."""
    try:
        # Get query parameters for filtering
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        employee_id = request.args.get('employee_id')
        status = request.args.get('status', 'staged')
        limit = int(request.args.get('limit', 1000))
        offset = int(request.args.get('offset', 0))
        
        conn = get_staging_db_connection()
        cursor = conn.cursor()
        
        # Build query with filters
        query = '''
            SELECT id, employee_id, employee_name, date, day_of_week, shift,
                   check_in, check_out, regular_hours, overtime_hours, total_hours,
                   task_code, station_code, machine_code, expense_code, status,
                   created_at, updated_at, source_record_id, notes
            FROM staging_attendance 
            WHERE 1=1
        '''
        params = []
        
        if start_date:
            query += ' AND date >= ?'
            params.append(start_date)
        if end_date:
            query += ' AND date <= ?'
            params.append(end_date)
        if employee_id:
            query += ' AND employee_id = ?'
            params.append(employee_id)
        if status:
            query += ' AND status = ?'
            params.append(status)
            
        query += ' ORDER BY date DESC, created_at DESC LIMIT ? OFFSET ?'
        params.extend([limit, offset])
        
        cursor.execute(query, params)
        rows = cursor.fetchall()
        
        # Get total count for pagination
        count_query = '''
            SELECT COUNT(*) FROM staging_attendance WHERE 1=1
        '''
        count_params = []
        
        if start_date:
            count_query += ' AND date >= ?'
            count_params.append(start_date)
        if end_date:
            count_query += ' AND date <= ?'
            count_params.append(end_date)
        if employee_id:
            count_query += ' AND employee_id = ?'
            count_params.append(employee_id)
        if status:
            count_query += ' AND status = ?'
            count_params.append(status)
            
        cursor.execute(count_query, count_params)
        total_count = cursor.fetchone()[0]
        
        conn.close()
        
        # Format data for JSON response
        formatted_data = []
        for row in rows:
            formatted_record = {
                'id': row[0],
                'employee_id': row[1],
                'employee_name': row[2],
                'date': row[3],
                'day_of_week': row[4],
                'shift': row[5],
                'check_in': row[6],
                'check_out': row[7],
                'regular_hours': round(float(row[8] or 0), 2),
                'overtime_hours': round(float(row[9] or 0), 2),
                'total_hours': round(float(row[10] or 0), 2),
                'task_code': row[11],
                'station_code': row[12],
                'machine_code': row[13],
                'expense_code': row[14],
                'status': row[15],
                'created_at': row[16],
                'updated_at': row[17],
                'source_record_id': row[18],
                'notes': row[19]
            }
            formatted_data.append(formatted_record)
        
        return jsonify({
            'success': True,
            'data': formatted_data,
            'total_records': total_count,
            'returned_records': len(formatted_data),
            'pagination': {
                'limit': limit,
                'offset': offset,
                'has_more': (offset + len(formatted_data)) < total_count
            }
        })
        
    except Exception as e:
        logger.error(f"Error getting staging data: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'Failed to retrieve staging data: {str(e)}'
        }), 500

@app.route('/api/staging/data', methods=['POST'])
def add_staging_data():
    """Add new records to staging."""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({
                'success': False,
                'error': 'No data provided'
            }), 400
        
        records = data.get('records', [])
        if not records:
            return jsonify({
                'success': False,
                'error': 'No records provided'
            }), 400
        
        conn = get_staging_db_connection()
        cursor = conn.cursor()
        
        added_records = []
        errors = []
        
        for record in records:
            try:
                # Generate unique ID for staging record
                staging_id = str(uuid.uuid4())
                
                # Extract record data
                employee_id = record.get('employee_id', '')
                employee_name = record.get('employee_name', '')
                date_str = record.get('date', '')
                day_of_week = record.get('day_of_week', '')
                shift = record.get('shift', '')
                check_in = record.get('check_in', '')
                check_out = record.get('check_out', '')
                regular_hours = float(record.get('regular_hours', 0))
                overtime_hours = float(record.get('overtime_hours', 0))
                total_hours = regular_hours + overtime_hours
                task_code = record.get('task_code', '')
                station_code = record.get('station_code', '')
                machine_code = record.get('machine_code', '')
                expense_code = record.get('expense_code', '')
                source_record_id = record.get('source_record_id', '')
                notes = record.get('notes', '')
                
                # Insert into staging table
                cursor.execute('''
                    INSERT INTO staging_attendance (
                        id, employee_id, employee_name, date, day_of_week, shift,
                        check_in, check_out, regular_hours, overtime_hours, total_hours,
                        task_code, station_code, machine_code, expense_code,
                        source_record_id, notes
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    staging_id, employee_id, employee_name, date_str, day_of_week, shift,
                    check_in, check_out, regular_hours, overtime_hours, total_hours,
                    task_code, station_code, machine_code, expense_code,
                    source_record_id, notes
                ))
                
                added_records.append(staging_id)
                
            except Exception as record_error:
                errors.append({
                    'record': record,
                    'error': str(record_error)
                })
                continue
        
        conn.commit()
        conn.close()
        
        return jsonify({
            'success': True,
            'added_records': len(added_records),
            'record_ids': added_records,
            'errors': errors,
            'message': f'Successfully added {len(added_records)} records to staging'
        })
        
    except Exception as e:
        logger.error(f"Error adding staging data: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'Failed to add staging data: {str(e)}'
        }), 500

@app.route('/api/staging/data/<staging_id>', methods=['PUT'])
def update_staging_record(staging_id):
    """Update an existing staging record."""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({
                'success': False,
                'error': 'No data provided'
            }), 400
        
        conn = get_staging_db_connection()
        cursor = conn.cursor()
        
        # Check if record exists
        cursor.execute('SELECT id FROM staging_attendance WHERE id = ?', (staging_id,))
        if not cursor.fetchone():
            conn.close()
            return jsonify({
                'success': False,
                'error': 'Staging record not found'
            }), 404
        
        # Build update query dynamically based on provided fields
        update_fields = []
        params = []
        
        updatable_fields = [
            'employee_id', 'employee_name', 'date', 'day_of_week', 'shift',
            'check_in', 'check_out', 'regular_hours', 'overtime_hours',
            'task_code', 'station_code', 'machine_code', 'expense_code',
            'status', 'notes'
        ]
        
        for field in updatable_fields:
            if field in data:
                update_fields.append(f'{field} = ?')
                params.append(data[field])
        
        if update_fields:
            # Always update the updated_at timestamp
            update_fields.append('updated_at = CURRENT_TIMESTAMP')
            
            # Recalculate total hours if regular or overtime hours changed
            if 'regular_hours' in data or 'overtime_hours' in data:
                # Get current values if not provided
                cursor.execute('SELECT regular_hours, overtime_hours FROM staging_attendance WHERE id = ?', (staging_id,))
                current_values = cursor.fetchone()
                
                regular_hours = data.get('regular_hours', current_values[0])
                overtime_hours = data.get('overtime_hours', current_values[1])
                total_hours = float(regular_hours) + float(overtime_hours)
                
                update_fields.append('total_hours = ?')
                params.append(total_hours)
            
            query = f'UPDATE staging_attendance SET {", ".join(update_fields)} WHERE id = ?'
            params.append(staging_id)
            
            cursor.execute(query, params)
            conn.commit()
        
        conn.close()
        
        return jsonify({
            'success': True,
            'message': 'Staging record updated successfully',
            'record_id': staging_id
        })
        
    except Exception as e:
        logger.error(f"Error updating staging record: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'Failed to update staging record: {str(e)}'
        }), 500

@app.route('/api/staging/data/<staging_id>', methods=['DELETE'])
def delete_staging_record(staging_id):
    """Remove a record from staging."""
    try:
        conn = get_staging_db_connection()
        cursor = conn.cursor()
        
        # Check if record exists
        cursor.execute('SELECT id FROM staging_attendance WHERE id = ?', (staging_id,))
        if not cursor.fetchone():
            conn.close()
            return jsonify({
                'success': False,
                'error': 'Staging record not found'
            }), 404
        
        # Delete the record
        cursor.execute('DELETE FROM staging_attendance WHERE id = ?', (staging_id,))
        conn.commit()
        conn.close()
        
        return jsonify({
            'success': True,
            'message': 'Staging record deleted successfully',
            'record_id': staging_id
        })
        
    except Exception as e:
        logger.error(f"Error deleting staging record: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'Failed to delete staging record: {str(e)}'
        }), 500

@app.route('/api/staging/upload', methods=['POST'])
def prepare_staging_upload():
    """Prepare staging data for database upload (future implementation)."""
    try:
        data = request.get_json()
        record_ids = data.get('record_ids', []) if data else []
        
        conn = get_staging_db_connection()
        cursor = conn.cursor()
        
        if record_ids:
            # Update specific records status to 'ready_for_upload'
            placeholders = ','.join(['?' for _ in record_ids])
            cursor.execute(f'''
                UPDATE staging_attendance 
                SET status = 'ready_for_upload', updated_at = CURRENT_TIMESTAMP 
                WHERE id IN ({placeholders})
            ''', record_ids)
        else:
            # Update all staged records
            cursor.execute('''
                UPDATE staging_attendance 
                SET status = 'ready_for_upload', updated_at = CURRENT_TIMESTAMP 
                WHERE status = 'staged'
            ''')
        
        affected_rows = cursor.rowcount
        conn.commit()
        conn.close()
        
        return jsonify({
            'success': True,
            'message': f'Prepared {affected_rows} records for upload',
            'affected_records': affected_rows,
            'status': 'ready_for_upload'
        })
        
    except Exception as e:
        logger.error(f"Error preparing staging upload: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'Failed to prepare staging upload: {str(e)}'
        }), 500

@app.route('/api/staging/stats', methods=['GET'])
def get_staging_stats():
    """Get staging statistics."""
    try:
        conn = get_staging_db_connection()
        cursor = conn.cursor()
        
        # Get counts by status
        cursor.execute('''
            SELECT status, COUNT(*) as count
            FROM staging_attendance 
            GROUP BY status
        ''')
        status_counts = dict(cursor.fetchall())
        
        # Get total records
        cursor.execute('SELECT COUNT(*) FROM staging_attendance')
        total_records = cursor.fetchone()[0]
        
        # Get date range
        cursor.execute('SELECT MIN(date), MAX(date) FROM staging_attendance')
        date_range = cursor.fetchone()
        
        conn.close()
        
        return jsonify({
            'success': True,
            'stats': {
                'total_records': total_records,
                'status_counts': status_counts,
                'date_range': {
                    'earliest': date_range[0],
                    'latest': date_range[1]
                }
            }
        })
        
    except Exception as e:
        logger.error(f"Error getting staging stats: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'Failed to get staging stats: {str(e)}'
        }), 500

def parse_charge_job_data(charge_job_string):
    """
    Parse charge job data string according to the specified format with enhanced edge case handling.
    
    Supports multiple formats:
    1. 4-component: TaskCode / StationCode / MachineCode / ExpenseCode
    2. 3-component: TaskCode / StationCode / ExpenseCode (no Machine Code)
    3. 2-component: TaskCode / ExpenseCode (no Station Code, no Machine Code)
    
    Handles edge cases:
    - Task codes containing forward slash characters (e.g., "C/Roll Wages")
    - Mixed separator formats
    - Partial or malformed data
    
    Args:
        charge_job_string (str): The raw charge job string from the spreadsheet
        
    Returns:
        dict: Parsed components with keys: task_code, station_code, machine_code, expense_code
    """
    if not charge_job_string or not isinstance(charge_job_string, str):
        return {
            'task_code': '',
            'station_code': '',
            'machine_code': '',
            'expense_code': '',
            'format_type': 'invalid',
            'parse_error': 'Empty or invalid charge job string'
        }
    
    try:
        # Get separators from config
        primary_separator = config.get("charge_job_parsing", {}).get("primary_separator", " / ")
        fallback_separator = config.get("charge_job_parsing", {}).get("fallback_separator", "/")
        
        # Clean the string
        cleaned_string = charge_job_string.strip()
        
        # First attempt: Use primary separator (space-slash-space)
        parts = [part.strip() for part in cleaned_string.split(primary_separator)]
        parts = [part for part in parts if part]  # Remove empty parts
        
        # If primary separator doesn't work well, try fallback strategy
        if len(parts) <= 1 and fallback_separator in cleaned_string:
            # Use fallback parsing for edge cases like task codes with embedded slashes
            parts = parse_with_fallback_strategy(cleaned_string, primary_separator, fallback_separator)
        
        # Determine format based on number of components
        if len(parts) == 4:
            # 4-component format: TaskCode / StationCode / MachineCode / ExpenseCode
            return {
                'task_code': parts[0],
                'station_code': parts[1],
                'machine_code': parts[2],
                'expense_code': parts[3],
                'format_type': '4_component',
                'parse_error': None
            }
        elif len(parts) == 3:
            # 3-component format: TaskCode / StationCode / ExpenseCode
            return {
                'task_code': parts[0],
                'station_code': parts[1],
                'machine_code': '',  # No machine code in 3-component format
                'expense_code': parts[2],
                'format_type': '3_component',
                'parse_error': None
            }
        elif len(parts) == 2:
            # 2-component format: TaskCode / ExpenseCode
            return {
                'task_code': parts[0],
                'station_code': '',  # No station code in 2-component format
                'machine_code': '',  # No machine code in 2-component format
                'expense_code': parts[1],
                'format_type': '2_component',
                'parse_error': None
            }
        elif len(parts) == 1:
            # Single component - treat as task code only
            return {
                'task_code': parts[0],
                'station_code': '',
                'machine_code': '',
                'expense_code': '',
                'format_type': '1_component',
                'parse_error': 'Only task code found, no separators'
            }
        else:
            # More than 4 components - try to handle gracefully
            return {
                'task_code': parts[0] if len(parts) > 0 else '',
                'station_code': parts[1] if len(parts) > 1 else '',
                'machine_code': parts[2] if len(parts) > 2 else '',
                'expense_code': parts[3] if len(parts) > 3 else '',
                'format_type': 'excessive_components',
                'parse_error': f'Found {len(parts)} components, expected 2-4. Using first 4 components.'
            }
            
    except Exception as e:
        return {
            'task_code': '',
            'station_code': '',
            'machine_code': '',
            'expense_code': '',
            'format_type': 'error',
            'parse_error': str(e)
        }

def parse_with_fallback_strategy(charge_job_string, primary_separator, fallback_separator):
    """
    Advanced parsing strategy to handle edge cases like task codes with embedded slashes.
    
    Strategy:
    1. Look for patterns that indicate legitimate separators vs embedded slashes
    2. Use context clues (spaces around separators, parentheses, etc.)
    3. Implement smart splitting based on common patterns
    
    Args:
        charge_job_string (str): The charge job string to parse
        primary_separator (str): Primary separator (space-slash-space)
        fallback_separator (str): Fallback separator (slash only)
        
    Returns:
        list: List of parsed components
    """
    try:
        # Strategy 1: Try to identify legitimate separators by looking for space patterns
        # Look for patterns like " / " which are more likely to be real separators
        if primary_separator in charge_job_string:
            # If we have primary separators, prefer those
            parts = [part.strip() for part in charge_job_string.split(primary_separator)]
            return [part for part in parts if part]
        
        # Strategy 2: If no primary separators, try intelligent fallback parsing
        # Split by fallback separator but try to identify task codes with embedded slashes
        tentative_parts = charge_job_string.split(fallback_separator)
        
        # Strategy 3: Enhanced heuristics for different scenarios
        if len(tentative_parts) >= 3:
            # Check for patterns that suggest we should combine parts for task codes
            
            # Scenario A: Simple format like "TaskCode/Station/Machine/Expense"
            # If parts look like structured data (no complex descriptions), treat as separate
            all_parts_simple = all(
                len(part.strip()) <= 30 and 
                '(' not in part and ')' not in part and
                ' AND ' not in part.upper() and
                ' OPERATION' not in part.upper()
                for part in tentative_parts
            )
            
            if all_parts_simple and len(tentative_parts) in [3, 4]:
                # This looks like a structured format: TaskCode/Station/Machine/Expense
                return [part.strip() for part in tentative_parts if part.strip()]
            
            # Scenario B: Task code with embedded slash (complex descriptions)
            first_part = tentative_parts[0].strip()
            second_part = tentative_parts[1].strip()
            
            # Enhanced heuristics for task codes with embedded slashes:
            # 1. Short first part + short second part (likely embedded slash)
            # 2. No complex descriptions in early parts
            # 3. Pattern like "Letter/Word" with reasonable length
            
            if (len(first_part) <= 15 and len(second_part) <= 25 and 
                '(' not in first_part and ')' not in first_part and
                '(' not in second_part and ')' not in second_part and
                ' STATION' not in second_part.upper() and
                ' STN-' not in second_part.upper() and
                'MCH-' not in second_part.upper()):
                
                # Likely a task code with embedded slash - combine first two parts
                combined_task_code = f"{first_part}/{second_part}"
                remaining_parts = [part.strip() for part in tentative_parts[2:] if part.strip()]
                
                result = [combined_task_code] + remaining_parts
                return result
        
        # Strategy 4: Default fallback - use tentative parts as-is
        return [part.strip() for part in tentative_parts if part.strip()]
        
    except Exception as e:
        # If all strategies fail, return original string as single component
        return [charge_job_string.strip()]

# Initialize the attendance reporter
try:
    reporter = AttendanceReporter()
    logger.info("AttendanceReporter initialized successfully")
except Exception as e:
    logger.error(f"Failed to initialize AttendanceReporter: {e}")
    reporter = None

@app.route('/')
def index():
    """Main dashboard page."""
    return render_template('index.html')

@app.route('/test')
def test_page():
    """Simple test page without DataTables."""
    return render_template('simple_test.html')

@app.route('/test-grid')
def test_grid_page():
    """Test page for grid functionality."""
    return render_template('test_grid.html')

@app.route('/test-charge-jobs')
def test_charge_jobs_page():
    """Test page for employee charge jobs integration."""
    return send_file('test_frontend_integration.html')

@app.route('/api/debug-attendance')
def debug_attendance():
    """Debug endpoint to check raw attendance data."""
    try:
        year = int(request.args.get('year', 2025))
        month = int(request.args.get('month', 3))
        bus_code = request.args.get('bus_code', 'PTRJ')

        # Get raw attendance data
        start_date = f"{year}-{month:02d}-01"
        end_date = f"{year}-{month:02d}-31"

        reporter = AttendanceReporter()
        raw_data = reporter.get_attendance_data(start_date, end_date, bus_code)

        # Get first 10 records for debugging
        debug_data = raw_data[:10] if raw_data else []

        return jsonify({
            'success': True,
            'total_records': len(raw_data),
            'sample_data': debug_data,
            'query_params': {
                'year': year,
                'month': month,
                'bus_code': bus_code,
                'start_date': start_date,
                'end_date': end_date
            }
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/test-grid-with-sample')
def test_grid_with_sample():
    """Test grid with sample working hours data."""
    try:
        # Generate sample data
        import calendar
        year = 2025
        month = 3
        days_in_month = calendar.monthrange(year, month)[1]

        # Sample employees
        employees = [
            {'EmployeeID': 'PTRJ.241000001', 'EmployeeName': 'John Doe'},
            {'EmployeeID': 'PTRJ.241000002', 'EmployeeName': 'Jane Smith'},
            {'EmployeeID': 'PTRJ.241000003', 'EmployeeName': 'Bob Wilson'},
        ]

        grid_data = []
        overtime_summary = []

        for i, emp in enumerate(employees, 1):
            days_data = {}
            total_regular = 0
            total_overtime = 0

            for day in range(1, days_in_month + 1):
                from datetime import datetime
                date_obj = datetime(year, month, day)
                weekday = date_obj.weekday()  # 0=Monday, 6=Sunday

                if weekday == 6:  # Sunday
                    days_data[str(day)] = 'OFF'
                elif day % 7 == 0:  # Some absent days
                    days_data[str(day)] = '-'
                elif weekday == 5:  # Saturday
                    hours = 4.5  # Saturday work
                    days_data[str(day)] = str(hours)
                    total_regular += hours
                else:  # Weekdays
                    if day % 5 == 0:  # Some overtime days
                        hours = 8.5
                        days_data[str(day)] = str(hours)
                        total_regular += 7
                        total_overtime += 1.5
                    else:
                        hours = 7.5
                        days_data[str(day)] = str(hours)
                        total_regular += hours

            grid_data.append({
                'No': i,
                'EmployeeID': emp['EmployeeID'],
                'EmployeeName': emp['EmployeeName'],
                'days': days_data
            })

            if total_overtime > 0:
                overtime_summary.append({
                    'No': i,
                    'EmployeeID': emp['EmployeeID'],
                    'EmployeeName': emp['EmployeeName'],
                    'TotalRegular': round(total_regular, 1),
                    'TotalOvertime': round(total_overtime, 1),
                    'TotalHours': round(total_regular + total_overtime, 1)
                })

        return jsonify({
            'success': True,
            'year': year,
            'month': month,
            'month_name': 'March',
            'days_in_month': days_in_month,
            'total_employees': len(employees),
            'total_working_days': 22,
            'grid_data': grid_data,
            'overtime_summary': overtime_summary,
            'date_range': 'March 2025'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/monthly-grid-by-station')
def monthly_grid_by_station():
    """Get monthly attendance grid grouped by stations."""
    try:
        year = int(request.args.get('year', 2025))
        month = int(request.args.get('month', 3))
        bus_code = request.args.get('bus_code')

        reporter = AttendanceReporter()
        grid_data = reporter.get_monthly_attendance_grid_by_station(year, month, bus_code)

        return jsonify({
            'success': True,
            'data': grid_data
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/test-monthly/<int:year>/<int:month>')
def test_monthly_direct(year, month):
    """Direct test for monthly report."""
    try:
        if reporter is None:
            return f"Error: AttendanceReporter not initialized"

        # Test get_monthly_summary
        summary = reporter.get_monthly_summary(year, month, "PTRJ")

        # Test get_attendance_data
        from datetime import datetime, timedelta
        start_date = f"{year}-{month:02d}-01"

        if month == 12:
            next_month_year = year + 1
            next_month = 1
        else:
            next_month_year = year
            next_month = month + 1

        end_date_obj = datetime(next_month_year, next_month, 1) - timedelta(days=1)
        end_date = end_date_obj.strftime("%Y-%m-%d")

        data = reporter.get_attendance_data(start_date, end_date, "PTRJ")

        return f"""
        <h1>Monthly Report Test: {year}-{month}</h1>
        <h2>Summary:</h2>
        <pre>{summary}</pre>
        <h2>Data Count:</h2>
        <p>Records found: {len(data)}</p>
        <h2>Sample Data:</h2>
        <pre>{data[:2] if data else 'No data'}</pre>
        """

    except Exception as e:
        import traceback
        return f"""
        <h1>Error Testing Monthly Report</h1>
        <p>Error: {str(e)}</p>
        <pre>{traceback.format_exc()}</pre>
        """

@app.route('/api/attendance')
def get_attendance_data():
    """
    API endpoint to get attendance data with filters.

    Query parameters:
    - start_date: Start date (YYYY-MM-DD)
    - end_date: End date (YYYY-MM-DD)
    - bus_code: Business code filter (optional)
    - employee_id: Employee ID filter (optional)
    - shift: Shift filter (optional)
    """
    try:
        # Get query parameters
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        bus_code = request.args.get('bus_code')
        employee_id = request.args.get('employee_id')
        shift = request.args.get('shift')

        # Validate required parameters
        if not start_date or not end_date:
            return jsonify({
                'error': 'start_date and end_date are required',
                'data': []
            }), 400

        # Get attendance data
        data = reporter.get_attendance_data(start_date, end_date, bus_code)

        # Apply additional filters
        if employee_id:
            data = [record for record in data if record.get('EmployeeID') == employee_id]

        if shift:
            data = [record for record in data if record.get('Shift') == shift]

        # Format data for JSON response
        formatted_data = []
        for record in data:
            formatted_record = {
                'EmployeeID': record.get('EmployeeID'),
                'EmployeeName': record.get('EmployeeName'),
                'Date': record.get('TADate').strftime('%Y-%m-%d') if record.get('TADate') else None,
                'Shift': record.get('Shift'),
                'CheckIn': record.get('TACheckIn').strftime('%H:%M') if record.get('TACheckIn') else None,
                'CheckOut': record.get('TACheckOut').strftime('%H:%M') if record.get('TACheckOut') else None,
                'RegularHours': round(float(record.get('RegularHours', 0)), 2),
                'OvertimeHours': round(float(record.get('OvertimeHours', 0)), 2),
                'DayOfWeek': record.get('DayOfWeek')
            }
            formatted_data.append(formatted_record)

        return jsonify({
            'success': True,
            'data': formatted_data,
            'total_records': len(formatted_data)
        })

    except Exception as e:
        return jsonify({
            'error': str(e),
            'data': []
        }), 500

@app.route('/api/employees')
def get_employees():
    """API endpoint to get list of employees."""
    try:
        bus_code = request.args.get('bus_code')
        employees = reporter.get_employees_list(bus_code)

        return jsonify({
            'success': True,
            'data': employees
        })

    except Exception as e:
        return jsonify({
            'error': str(e),
            'data': []
        }), 500

@app.route('/api/shifts')
def get_shifts():
    """API endpoint to get list of shifts."""
    try:
        bus_code = request.args.get('bus_code')
        shifts = reporter.get_shifts_list(bus_code)

        return jsonify({
            'success': True,
            'data': shifts
        })

    except Exception as e:
        return jsonify({
            'error': str(e),
            'data': []
        }), 500

@app.route('/api/export')
def export_report():
    """API endpoint to export attendance report to Excel or JSON."""
    try:
        # Get query parameters
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        bus_code = request.args.get('bus_code')
        employee_id = request.args.get('employee_id')
        shift = request.args.get('shift')
        format_type = request.args.get('format', 'excel')  # 'excel' or 'json'

        # Validate required parameters
        if not start_date or not end_date:
            return jsonify({
                'error': 'start_date and end_date are required'
            }), 400

        # Get attendance data
        data = reporter.get_attendance_data(start_date, end_date, bus_code)
        
        # Filter data if specific employee or shift requested
        if employee_id:
            data = [record for record in data if record.get('EmployeeID') == employee_id]
        if shift:
            data = [record for record in data if record.get('Shift') == shift]

        if format_type == 'json':
            # JSON export
            import tempfile
            import json
            
            # Prepare JSON data with metadata
            json_data = {
                'metadata': {
                    'start_date': start_date,
                    'end_date': end_date,
                    'bus_code': bus_code,
                    'employee_id': employee_id,
                    'shift': shift,
                    'export_date': datetime.now().isoformat(),
                    'total_records': len(data)
                },
                'attendance_data': []
            }
            
            # Format attendance data
            for record in data:
                formatted_record = {
                    'EmployeeID': record.get('EmployeeID', ''),
                    'EmployeeName': record.get('EmployeeName', ''),
                    'Date': record.get('TADate').strftime('%Y-%m-%d') if record.get('TADate') else '',
                    'DayOfWeek': record.get('DayOfWeek', ''),
                    'Shift': record.get('Shift', ''),
                    'CheckIn': str(record.get('TACheckIn', '')) if record.get('TACheckIn') else '',
                    'CheckOut': str(record.get('TACheckOut', '')) if record.get('TACheckOut') else '',
                    'RegularHours': float(record.get('RegularHours', 0)),
                    'OvertimeHours': float(record.get('OvertimeHours', 0)),
                    'TotalHours': float(record.get('RegularHours', 0)) + float(record.get('OvertimeHours', 0))
                }
                json_data['attendance_data'].append(formatted_record)
            
            # Create temporary JSON file
            temp_dir = tempfile.gettempdir()
            filename = f"attendance_report_{start_date}_to_{end_date}.json"
            json_path = os.path.join(temp_dir, filename)
            
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump(json_data, f, indent=2, ensure_ascii=False, default=str)
            
            return send_file(json_path, as_attachment=True, download_name=filename)
            
        else:
            # Excel export (existing functionality)
            if start_date == end_date:
                # Daily report
                excel_path = reporter.generate_daily_report(start_date, bus_code)
            else:
                # Date range report
                excel_path = reporter.generate_date_range_report(start_date, end_date, bus_code)

            if excel_path and os.path.exists(excel_path):
                return send_file(
                    excel_path,
                    as_attachment=True,
                    download_name=os.path.basename(excel_path),
                    mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                )
            else:
                return jsonify({
                    'error': 'No data found or report generation failed'
                }), 404

    except Exception as e:
        return jsonify({
            'error': str(e)
        }), 500

@app.route('/api/summary')
def get_summary():
    """API endpoint to get attendance summary statistics."""
    try:
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        bus_code = request.args.get('bus_code')

        if not start_date or not end_date:
            return jsonify({
                'error': 'start_date and end_date are required'
            }), 400

        # Get attendance data
        data = reporter.get_attendance_data(start_date, end_date, bus_code)

        # Calculate summary statistics
        total_employees = len(set(record.get('EmployeeID') for record in data))
        total_records = len(data)
        total_regular_hours = sum(float(record.get('RegularHours', 0)) for record in data)
        total_overtime_hours = sum(float(record.get('OvertimeHours', 0)) for record in data)

        # Calculate average hours per employee
        avg_regular_hours = total_regular_hours / total_employees if total_employees > 0 else 0
        avg_overtime_hours = total_overtime_hours / total_employees if total_employees > 0 else 0

        summary = {
            'total_employees': total_employees,
            'total_records': total_records,
            'total_regular_hours': round(total_regular_hours, 2),
            'total_overtime_hours': round(total_overtime_hours, 2),
            'avg_regular_hours': round(avg_regular_hours, 2),
            'avg_overtime_hours': round(avg_overtime_hours, 2),
            'date_range': f"{start_date} to {end_date}"
        }

        return jsonify({
            'success': True,
            'data': summary
        })

    except Exception as e:
        return jsonify({
            'error': str(e)
        }), 500

@app.route('/api/months')
def get_available_months():
    """API endpoint to get list of available months with data."""
    try:
        if reporter is None:
            logger.error("AttendanceReporter not initialized")
            return jsonify({
                'error': 'Database connection not available',
                'data': []
            }), 500

        bus_code = request.args.get('bus_code')
        logger.info(f"Getting available months with bus_code: {bus_code}")

        months = reporter.get_available_months(bus_code)
        logger.info(f"Retrieved {len(months)} months from reporter")

        # Format months for frontend
        formatted_months = []
        for month in months:
            try:
                formatted_month = {
                    'year': month.get('Year'),
                    'month': month.get('Month'),
                    'month_name': month.get('MonthName'),
                    'record_count': month.get('RecordCount'),
                    'employee_count': month.get('EmployeeCount'),
                    'first_date': month.get('FirstDate').strftime('%Y-%m-%d') if month.get('FirstDate') else None,
                    'last_date': month.get('LastDate').strftime('%Y-%m-%d') if month.get('LastDate') else None,
                    'display_name': f"{month.get('MonthName')} {month.get('Year')}",
                    'month_key': f"{month.get('Year')}-{month.get('Month'):02d}"
                }
                formatted_months.append(formatted_month)
            except Exception as format_error:
                logger.error(f"Error formatting month {month}: {format_error}")
                continue

        logger.info(f"Successfully formatted {len(formatted_months)} months")

        return jsonify({
            'success': True,
            'data': formatted_months,
            'debug_info': {
                'raw_months_count': len(months),
                'formatted_months_count': len(formatted_months),
                'bus_code': bus_code
            }
        })

    except Exception as e:
        logger.error(f"Error in get_available_months: {str(e)}")
        import traceback
        logger.error(f"Full traceback: {traceback.format_exc()}")
        return jsonify({
            'error': str(e),
            'data': [],
            'debug_info': {
                'error_type': type(e).__name__,
                'bus_code': request.args.get('bus_code')
            }
        }), 500

@app.route('/api/monthly-report')
def get_monthly_report():
    """API endpoint to get monthly attendance report."""
    try:
        if reporter is None:
            logger.error("AttendanceReporter not initialized")
            return jsonify({
                'error': 'Database connection not available',
                'data': []
            }), 500

        year = request.args.get('year', type=int)
        month = request.args.get('month', type=int)
        bus_code = request.args.get('bus_code')

        logger.info(f"Getting monthly report for {year}-{month}, bus_code: {bus_code}")

        if not year or not month:
            logger.error("Missing year or month parameter")
            return jsonify({
                'error': 'year and month are required'
            }), 400

        if month < 1 or month > 12:
            logger.error(f"Invalid month: {month}")
            return jsonify({
                'error': 'month must be between 1 and 12'
            }), 400

        # Get monthly summary
        logger.info("Getting monthly summary...")
        summary = reporter.get_monthly_summary(year, month, bus_code)
        logger.info(f"Monthly summary retrieved: {summary}")

        # Get detailed attendance data for the month
        from datetime import datetime, timedelta
        start_date = f"{year}-{month:02d}-01"

        # Calculate the last day of the month
        if month == 12:
            next_month_year = year + 1
            next_month = 1
        else:
            next_month_year = year
            next_month = month + 1

        end_date_obj = datetime(next_month_year, next_month, 1) - timedelta(days=1)
        end_date = end_date_obj.strftime("%Y-%m-%d")

        logger.info(f"Getting attendance data from {start_date} to {end_date}")

        # Get attendance data
        data = reporter.get_attendance_data(start_date, end_date, bus_code)
        logger.info(f"Retrieved {len(data)} attendance records")

        # Format data for JSON response
        formatted_data = []
        for record in data:
            try:
                formatted_record = {
                    'EmployeeID': record.get('EmployeeID'),
                    'EmployeeName': record.get('EmployeeName'),
                    'Date': record.get('TADate').strftime('%Y-%m-%d') if record.get('TADate') else None,
                    'Shift': record.get('Shift'),
                    'CheckIn': record.get('TACheckIn').strftime('%H:%M') if record.get('TACheckIn') else None,
                    'CheckOut': record.get('TACheckOut').strftime('%H:%M') if record.get('TACheckOut') else None,
                    'RegularHours': round(float(record.get('RegularHours', 0)), 2),
                    'OvertimeHours': round(float(record.get('OvertimeHours', 0)), 2),
                    'DayOfWeek': record.get('DayOfWeek')
                }
                formatted_data.append(formatted_record)
            except Exception as format_error:
                logger.error(f"Error formatting record {record}: {format_error}")
                continue

        logger.info(f"Successfully formatted {len(formatted_data)} records")

        return jsonify({
            'success': True,
            'summary': summary,
            'data': formatted_data,
            'total_records': len(formatted_data),
            'debug_info': {
                'year': year,
                'month': month,
                'bus_code': bus_code,
                'date_range': f"{start_date} to {end_date}",
                'raw_records': len(data),
                'formatted_records': len(formatted_data)
            }
        })

    except Exception as e:
        logger.error(f"Error in get_monthly_report: {str(e)}")
        import traceback
        logger.error(f"Full traceback: {traceback.format_exc()}")
        return jsonify({
            'error': str(e),
            'debug_info': {
                'year': request.args.get('year'),
                'month': request.args.get('month'),
                'bus_code': request.args.get('bus_code'),
                'error_type': type(e).__name__
            }
        }), 500

@app.route('/api/monthly-grid')
def get_monthly_attendance_grid():
    """API endpoint to get monthly attendance in grid format (employee vs days)."""
    try:
        if reporter is None:
            logger.error("AttendanceReporter not initialized")
            return jsonify({
                'error': 'Database connection not available',
                'data': []
            }), 500

        year = request.args.get('year', type=int)
        month = request.args.get('month', type=int)
        bus_code = request.args.get('bus_code')

        logger.info(f"Getting monthly grid for {year}-{month}, bus_code: {bus_code}")

        if not year or not month:
            logger.error("Missing year or month parameter")
            return jsonify({
                'error': 'year and month are required'
            }), 400

        if month < 1 or month > 12:
            logger.error(f"Invalid month: {month}")
            return jsonify({
                'error': 'month must be between 1 and 12'
            }), 400

        # Get grid data
        logger.info("Getting monthly attendance grid...")
        grid_data = reporter.get_monthly_attendance_grid(year, month, bus_code)
        logger.info(f"Grid data retrieved: {len(grid_data.get('grid_data', []))} employees")

        return jsonify({
            'success': True,
            'data': grid_data,
            'debug_info': {
                'year': year,
                'month': month,
                'bus_code': bus_code,
                'total_employees': grid_data.get('total_employees', 0),
                'days_in_month': grid_data.get('days_in_month', 0)
            }
        })

    except Exception as e:
        logger.error(f"Error in get_monthly_attendance_grid: {str(e)}")
        import traceback
        logger.error(f"Full traceback: {traceback.format_exc()}")
        return jsonify({
            'error': str(e),
            'debug_info': {
                'year': request.args.get('year'),
                'month': request.args.get('month'),
                'bus_code': request.args.get('bus_code'),
                'error_type': type(e).__name__
            }
        }), 500

@app.route('/api/debug')
def debug_info():
    """Debug endpoint to check system status."""
    try:
        debug_data = {
            'reporter_status': 'initialized' if reporter else 'failed',
            'python_path': sys.path[:3],
            'current_directory': os.getcwd(),
            'templates_exist': os.path.exists('templates'),
            'static_exist': os.path.exists('static')
        }

        if reporter:
            try:
                # Test database connection
                test_months = reporter.get_available_months()
                debug_data['database_test'] = {
                    'status': 'success',
                    'months_found': len(test_months),
                    'sample_month': test_months[0] if test_months else None
                }
            except Exception as db_error:
                debug_data['database_test'] = {
                    'status': 'failed',
                    'error': str(db_error)
                }

        return jsonify({
            'success': True,
            'debug_data': debug_data
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/export-grid', methods=['GET'])
def export_grid():
    """
    Export attendance grid data to Excel or JSON format.
    Supports both regular grid and station-grouped grid.
    """
    try:
        # Get parameters
        year = request.args.get('year', type=int)
        month = request.args.get('month', type=int)
        bus_code = request.args.get('bus_code', 'PTRJ')
        format_type = request.args.get('format', 'excel')  # 'excel' or 'json'
        grid_type = request.args.get('type', 'grid')  # 'grid' or 'station-grid'

        if not year or not month:
            return jsonify({'success': False, 'error': 'Year and month are required'}), 400

        logger.info(f"Exporting {grid_type} data: {year}-{month}, format: {format_type}")

        # Get the appropriate grid data
        if grid_type == 'station-grid':
            grid_data = reporter.get_monthly_attendance_grid_by_station(year, month, bus_code)
        else:
            grid_data = reporter.get_monthly_attendance_grid(year, month, bus_code)

        if not grid_data:
            return jsonify({'success': False, 'error': 'No data found for the specified month'}), 404

        # Generate filename
        month_name = grid_data.get('month_name', f'Month{month}')
        grid_suffix = '_by_station' if grid_type == 'station-grid' else ''
        
        if format_type == 'json':
            filename = f"attendance_grid{grid_suffix}_{month_name}_{year}.json"
            
            # Prepare JSON data
            json_data = {
                'metadata': {
                    'year': year,
                    'month': month,
                    'month_name': month_name,
                    'days_in_month': grid_data.get('days_in_month'),
                    'total_employees': grid_data.get('total_employees'),
                    'export_date': datetime.now().isoformat(),
                    'business_code': bus_code,
                    'grid_type': grid_type
                },
                'grid_data': grid_data.get('stations_grid' if grid_type == 'station-grid' else 'grid_data', []),
                'overtime_summary': grid_data.get('overtime_summary', [])
            }
            
            # Create JSON file
            import json
            import tempfile
            import os
            
            temp_dir = tempfile.gettempdir()
            json_path = os.path.join(temp_dir, filename)
            
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump(json_data, f, indent=2, ensure_ascii=False, default=str)
            
            return send_file(json_path, as_attachment=True, download_name=filename)
            
        else:  # Excel format
            filename = f"attendance_grid{grid_suffix}_{month_name}_{year}.xlsx"
            
            # Create Excel workbook using xlsxwriter to match web format
            import xlsxwriter
            import tempfile
            import os
            
            temp_dir = tempfile.gettempdir()
            excel_path = os.path.join(temp_dir, filename)
            
            workbook = xlsxwriter.Workbook(excel_path)
            worksheet = workbook.add_worksheet('Attendance Grid')
            
            # Define formats
            header_format = workbook.add_format({
                'bold': True,
                'bg_color': '#212529',
                'font_color': 'white',
                'align': 'center',
                'valign': 'vcenter',
                'border': 1
            })
            
            sunday_header_format = workbook.add_format({
                'bold': True,
                'bg_color': '#dc3545',
                'font_color': 'white',
                'align': 'center',
                'valign': 'vcenter',
                'border': 1
            })
            
            total_header_format = workbook.add_format({
                'bold': True,
                'bg_color': '#198754',
                'font_color': 'white',
                'align': 'center',
                'valign': 'vcenter',
                'border': 1
            })
            
            station_format = workbook.add_format({
                'bold': True,
                'bg_color': '#6c757d',
                'font_color': 'white',
                'align': 'left',
                'valign': 'vcenter',
                'border': 1
            })
            
            total_cell_format = workbook.add_format({
                'bg_color': '#e8f5e8',
                'bold': True,
                'align': 'center',
                'valign': 'vcenter',
                'border': 1
            })
            
            # Regular cell formats for attendance
            full_format = workbook.add_format({'bg_color': '#d1e7dd', 'align': 'center', 'border': 1})
            partial_format = workbook.add_format({'bg_color': '#f8d7da', 'align': 'center', 'border': 1})
            off_format = workbook.add_format({'bg_color': '#d1ecf1', 'align': 'center', 'border': 1})
            absent_format = workbook.add_format({'bg_color': '#f8f9fa', 'align': 'center', 'border': 1})
            
            # Write headers
            headers = ['No', 'Employee ID', 'Employee Name']
            
            # Add day headers
            for day in range(1, grid_data.get('days_in_month', 31) + 1):
                from datetime import datetime
                date_obj = datetime(year, month, day)
                day_name = ['Min', 'Sen', 'Sel', 'Rab', 'Kam', 'Jum', 'Sab'][date_obj.weekday()]
                is_sunday = date_obj.weekday() == 6
                
                headers.append(f"{day}\n{day_name}")
                
                # Apply format based on day
                format_to_use = sunday_header_format if is_sunday else header_format
                worksheet.write(0, len(headers) - 1, f"{day}\n{day_name}", format_to_use)
            
            # Add total headers (3 columns)
            worksheet.write(0, len(headers), "DAYS\nTotal", total_header_format)
            worksheet.write(0, len(headers) + 1, "REG\nHours", total_header_format)
            worksheet.write(0, len(headers) + 2, "OT\nHours", total_header_format)
            
            # Write basic headers first
            for i, header in enumerate(headers[:3]):
                worksheet.write(0, i, header, header_format)
            
            current_row = 1
            
            # Write data based on grid type
            if grid_type == 'station-grid':
                stations_data = grid_data.get('stations_grid', {})
                
                for station_name, employees in stations_data.items():
                    # Write station header
                    worksheet.merge_range(current_row, 0, current_row, len(headers) + 2, 
                                        f"{station_name} ({len(employees)} employees)", station_format)
                    current_row += 1
                    
                    # Write employees
                    for employee in employees:
                        worksheet.write(current_row, 0, employee.get('No'), absent_format)
                        worksheet.write(current_row, 1, employee.get('EmployeeID'), absent_format)
                        worksheet.write(current_row, 2, employee.get('EmployeeName'), absent_format)
                        
                        # Calculate totals
                        total_days = 0
                        total_regular = 0
                        total_overtime = 0
                        
                        # Write daily data
                        for day in range(1, grid_data.get('days_in_month', 31) + 1):
                            hours = employee.get('days', {}).get(str(day), '-')
                            col = 3 + day - 1
                            
                            # Determine cell format
                            cell_format = absent_format
                            if hours == 'OFF':
                                cell_format = off_format
                            elif hours != '-' and '|' in hours:
                                parts = hours.split('|')
                                if len(parts) == 2:
                                    regular_part = parts[0].strip().replace('(', '').replace(')', '')
                                    regular_hours = float(regular_part) if regular_part.replace('.', '').isdigit() else 0
                                    
                                    # Determine if Saturday
                                    date_obj = datetime(year, month, day)
                                    is_saturday = date_obj.weekday() == 5
                                    threshold = 5 if is_saturday else 7
                                    
                                    if regular_hours >= threshold:
                                        cell_format = full_format
                                    elif regular_hours > 0:
                                        cell_format = partial_format
                                    
                                    # Calculate totals
                                    overtime_part = parts[1].strip().replace('(', '').replace(')', '')
                                    overtime_hours = float(overtime_part) if overtime_part != '-' and overtime_part.replace('.', '').isdigit() else 0
                                    
                                    if regular_hours > 0 or overtime_hours > 0:
                                        total_days += 1
                                        total_regular += regular_hours
                                        total_overtime += overtime_hours
                            
                            worksheet.write(current_row, col, hours, cell_format)
                        
                        # Write totals (3 separate cells)
                        worksheet.write(current_row, len(headers), total_days, total_cell_format)
                        worksheet.write(current_row, len(headers) + 1, round(total_regular, 1), total_cell_format)
                        worksheet.write(current_row, len(headers) + 2, round(total_overtime, 1), total_cell_format)
                        
                        current_row += 1
            else:
                # Regular grid
                employees = grid_data.get('grid_data', [])
                
                for employee in employees:
                    worksheet.write(current_row, 0, employee.get('No'), absent_format)
                    worksheet.write(current_row, 1, employee.get('EmployeeID'), absent_format)
                    worksheet.write(current_row, 2, employee.get('EmployeeName'), absent_format)
                    
                    # Calculate totals
                    total_days = 0
                    total_regular = 0
                    total_overtime = 0
                    
                    # Write daily data
                    for day in range(1, grid_data.get('days_in_month', 31) + 1):
                        hours = employee.get('days', {}).get(str(day), '-')
                        col = 3 + day - 1
                        
                        # Determine cell format and calculate totals
                        cell_format = absent_format
                        if hours == 'OFF':
                            cell_format = off_format
                        elif hours != '-' and '|' in hours:
                            parts = hours.split('|')
                            if len(parts) == 2:
                                regular_part = parts[0].strip().replace('(', '').replace(')', '')
                                regular_hours = float(regular_part) if regular_part.replace('.', '').isdigit() else 0
                                
                                # Determine if Saturday
                                date_obj = datetime(year, month, day)
                                is_saturday = date_obj.weekday() == 5
                                threshold = 5 if is_saturday else 7
                                
                                if regular_hours >= threshold:
                                    cell_format = full_format
                                elif regular_hours > 0:
                                    cell_format = partial_format
                                
                                # Calculate totals
                                overtime_part = parts[1].strip().replace('(', '').replace(')', '')
                                overtime_hours = float(overtime_part) if overtime_part != '-' and overtime_part.replace('.', '').isdigit() else 0
                                
                                if regular_hours > 0 or overtime_hours > 0:
                                    total_days += 1
                                    total_regular += regular_hours
                                    total_overtime += overtime_hours
                        
                        worksheet.write(current_row, col, hours, cell_format)
                    
                    # Write totals (3 separate cells)
                    worksheet.write(current_row, len(headers), total_days, total_cell_format)
                    worksheet.write(current_row, len(headers) + 1, round(total_regular, 1), total_cell_format)
                    worksheet.write(current_row, len(headers) + 2, round(total_overtime, 1), total_cell_format)
                    
                    current_row += 1
            
            # Set column widths
            worksheet.set_column(0, 0, 8)   # No
            worksheet.set_column(1, 1, 15)  # Employee ID
            worksheet.set_column(2, 2, 25)  # Employee Name
            worksheet.set_column(3, len(headers) + 2, 12)  # Day columns and totals
            
            workbook.close()
            
            return send_file(excel_path, as_attachment=True, download_name=filename)
            
    except Exception as e:
        logger.error(f"Error in grid export: {str(e)}")
        import traceback
        logger.error(f"Full traceback: {traceback.format_exc()}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/employee-charge-jobs')
def get_employee_charge_jobs():
    """
    Fetch employee charge job data from Google Apps Script API.
    Returns parsed task codes, station codes, machine codes, and expense codes.
    """
    try:
        logger.info(f"Fetching employee charge job data from: {CHARGE_JOB_DATA_URL}")
        
        # Make GET request to Google Apps Script for charge job data
        timeout = config.get("sync_settings", {}).get("timeout_seconds", 30)
        response = requests.get(CHARGE_JOB_DATA_URL, timeout=timeout)
        response.raise_for_status()
        
        # Parse the response
        try:
            employee_data = response.json()
            logger.info(f"Retrieved response from Google Apps Script")
            logger.info(f"Response type: {type(employee_data)}")
            
            # Handle different response formats
            if isinstance(employee_data, dict):
                # If response is wrapped in a result object
                if 'data' in employee_data:
                    employee_list = employee_data['data']
                elif 'employees' in employee_data:
                    employee_list = employee_data['employees']
                else:
                    # Assume the dict itself contains employee data
                    employee_list = [employee_data]
            else:
                # Assume it's already a list
                employee_list = employee_data
            
            logger.info(f"Processing {len(employee_list)} employee records")
            
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse JSON response: {e}")
            logger.error(f"Response text: {response.text[:500]}...")
            return jsonify({
                'success': False,
                'error': 'Invalid JSON response from charge job data source',
                'debug_info': {
                    'response_status': response.status_code,
                    'response_preview': response.text[:200]
                }
            }), 500
        
        # Process and parse the charge job data
        processed_data = {}
        parse_errors = []
        format_stats = {'4_component': 0, '3_component': 0, '2_component': 0, '1_component': 0, 'partial': 0, 'error': 0, 'invalid': 0, 'excessive_components': 0}
        
        for i, emp in enumerate(employee_list):
            try:
                # Handle different field name variations for employee identification
                emp_name = (emp.get('namaKaryawan') or 
                           emp.get('employeeName') or 
                           emp.get('EmployeeName') or 
                           emp.get('name') or '').strip()
                
                emp_id = (emp.get('employeeId') or 
                         emp.get('EmployeeID') or 
                         emp.get('employeeID') or 
                         emp.get('id') or '').strip()
                
                # Handle different field name variations for charge job data
                charge_job = (emp.get('chargeJob') or 
                             emp.get('charge_job') or 
                             emp.get('ChargeJob') or 
                             emp.get('task_code_data') or '').strip()
                
                # Use employee ID as primary key, fallback to name
                employee_key = emp_id if emp_id else emp_name
                
                if employee_key and charge_job:
                    # Parse the charge job data using our new parsing function
                    parsed_data = parse_charge_job_data(charge_job)
                    
                    # Track format statistics
                    format_type = parsed_data.get('format_type', 'unknown')
                    if format_type in format_stats:
                        format_stats[format_type] += 1
                    
                    # Log parsing errors for debugging
                    if parsed_data.get('parse_error'):
                        parse_errors.append({
                            'employee': employee_key,
                            'charge_job': charge_job,
                            'error': parsed_data['parse_error']
                        })
                    
                    # Prepare the final employee data
                    processed_data[employee_key] = {
                        'employee_id': emp_id,
                        'employee_name': emp_name,
                        'task_code': parsed_data['task_code'],
                        'station_code': parsed_data['station_code'],
                        'machine_code': parsed_data['machine_code'], 
                        'expense_code': parsed_data['expense_code'],
                        'raw_charge_job': charge_job,
                        'format_type': parsed_data['format_type'],
                        'parse_error': parsed_data['parse_error'],
                        
                        # Additional employee data if available
                        'gender': emp.get('Gender') or emp.get('gender', ''),
                        'station': emp.get('Nama Stasiun') or emp.get('station', ''),
                        'no_urut': emp.get('No. Urut') or emp.get('sequence_number', '')
                    }
                else:
                    # Log missing data
                    missing_fields = []
                    if not employee_key:
                        missing_fields.append('employee identifier (name/ID)')
                    if not charge_job:
                        missing_fields.append('charge job data')
                    
                    logger.warning(f"Skipping employee record {i+1}: missing {', '.join(missing_fields)}")
                    
            except Exception as emp_error:
                logger.error(f"Error processing employee record {i+1}: {emp_error}")
                parse_errors.append({
                    'employee': f'Record {i+1}',
                    'charge_job': str(emp),
                    'error': str(emp_error)
                })
                continue
        
        logger.info(f"Successfully processed {len(processed_data)} employee charge job records")
        logger.info(f"Format statistics: {format_stats}")
        
        if parse_errors:
            logger.warning(f"Encountered {len(parse_errors)} parsing errors")
            for error in parse_errors[:5]:  # Log first 5 errors
                logger.warning(f"  - {error['employee']}: {error['error']}")
        
        return jsonify({
            'success': True,
            'data': processed_data,
            'total_records': len(processed_data),
            'source_url': CHARGE_JOB_DATA_URL,
            'format_statistics': format_stats,
            'parse_errors': len(parse_errors),
            'debug_info': {
                'raw_employee_count': len(employee_list),
                'processed_count': len(processed_data),
                'sample_errors': parse_errors[:3] if parse_errors else []
            }
        })
        
    except requests.exceptions.Timeout:
        logger.error("Timeout while fetching charge job data from Google Apps Script")
        return jsonify({
            'success': False,
            'error': 'Request timeout while fetching employee charge job data from Google Sheets',
            'source_url': CHARGE_JOB_DATA_URL
        }), 408
        
    except requests.exceptions.RequestException as e:
        logger.error(f"Request error while fetching charge job data: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'Failed to fetch employee charge job data: {str(e)}',
            'source_url': CHARGE_JOB_DATA_URL
        }), 500
        
    except Exception as e:
        logger.error(f"Error processing employee charge job data: {str(e)}")
        import traceback
        logger.error(f"Full traceback: {traceback.format_exc()}")
        return jsonify({
            'success': False,
            'error': f'Error processing employee charge job data: {str(e)}',
            'source_url': CHARGE_JOB_DATA_URL,
            'debug_info': {
                'error_type': type(e).__name__
            }
        }), 500

@app.route('/api/sync-to-spreadsheet', methods=['POST'])
def sync_to_spreadsheet():
    """Proxy endpoint to sync data to Google Apps Script."""
    try:
        # Get data from request
        data = request.get_json()
        
        if not data:
            return jsonify({
                'success': False,
                'error': 'No data provided'
            }), 400
            
        sync_url = data.get('sync_url')
        action = data.get('action')
        sync_data = data.get('data')
        
        if not sync_url:
            return jsonify({
                'success': False,
                'error': 'sync_url is required'
            }), 400
            
        if not action:
            return jsonify({
                'success': False,
                'error': 'action is required'
            }), 400
            
        if not sync_data:
            return jsonify({
                'success': False,
                'error': 'data is required'
            }), 400
        
        logger.info(f"=== Received sync request ===")
        logger.info(f"  - sync_url: {sync_url}")
        logger.info(f"  - action: {action}")
        logger.info(f"  - data type: {type(sync_data)}")
        logger.info(f"  - data length: {len(sync_data) if isinstance(sync_data, list) else 'Unknown'}")
        
        if isinstance(sync_data, list) and len(sync_data) > 0:
            sample_record = sync_data[0]
            logger.info(f"  - sample record: {sample_record}")
        
        logger.info(f"Proxying sync request to: {sync_url}")
        logger.info(f"Action: {action}")
        logger.info(f"Data records: {len(sync_data) if isinstance(sync_data, list) else 'Unknown'}")
        
        # Prepare data for Google Apps Script
        # Try POST first, fallback to GET with optimized data if POST fails

        # Optimize data for sync (remove large status objects to reduce payload size)
        optimized_sync_data = []
        if isinstance(sync_data, list):
            for record in sync_data:
                if isinstance(record, dict):
                    # Create optimized record without large status data
                    optimized_record = {}
                    for key, value in record.items():
                        # Skip status objects that make payload too large
                        if not key.endswith('_status'):
                            optimized_record[key] = value
                    optimized_sync_data.append(optimized_record)
                else:
                    optimized_sync_data.append(record)
        else:
            optimized_sync_data = sync_data

        data_json = json.dumps(optimized_sync_data) if isinstance(optimized_sync_data, (list, dict)) else optimized_sync_data

        logger.info(f"Attempting sync to Google Apps Script: action={action}")
        logger.info(f"Original data length: {len(json.dumps(sync_data)) if isinstance(sync_data, (list, dict)) else len(str(sync_data))}")
        logger.info(f"Optimized data length: {len(data_json)}")

        # Try POST first (for updated Google Apps Script)
        try:
            payload = {
                'action': action,
                'data': data_json
            }

            logger.info("Trying POST request...")
            headers = {'Content-Type': 'application/json'}
            response = requests.post(sync_url, json=payload, headers=headers, timeout=30)

            # Check if POST was successful
            if response.status_code == 200:
                try:
                    result = response.json()
                    if result.get('status') != 'error' or 'Invalid POST action' not in result.get('message', ''):
                        logger.info("POST request successful")
                    else:
                        raise Exception("POST not supported, falling back to GET")
                except:
                    raise Exception("POST response invalid, falling back to GET")
            else:
                raise Exception(f"POST failed with status {response.status_code}")

        except Exception as post_error:
            logger.info(f"POST failed ({post_error}), falling back to GET request...")

            # Fallback to GET request with URL parameters
            params = {
                'action': action,
                'data': data_json
            }

            # Check if data is too large for GET
            if len(data_json) > 8000:  # Conservative limit for URL parameters
                logger.warning(f"Data too large for GET request ({len(data_json)} chars), truncating...")
                # Further optimize by keeping only essential fields
                if isinstance(optimized_sync_data, list) and len(optimized_sync_data) > 0:
                    # Keep only first few records for demonstration
                    truncated_data = optimized_sync_data[:2]  # Keep only 2 records
                    params['data'] = json.dumps(truncated_data)
                    logger.info(f"Truncated to {len(truncated_data)} records")

            response = requests.get(sync_url, params=params, timeout=30)
        
        logger.info(f"Google Apps Script response status: {response.status_code}")
        logger.info(f"Google Apps Script response: {response.text[:500]}...")  # Log first 500 chars
        
        # Try to parse response as JSON
        try:
            result = response.json()
            logger.info(f"Parsed result: {result}")
        except:
            # If not JSON, treat as successful if status is 200
            if response.status_code == 200:
                result = {
                    'status': 'success',
                    'message': 'Data synced successfully',
                    'raw_response': response.text
                }
            else:
                result = {
                    'status': 'error',
                    'message': f'HTTP {response.status_code}: {response.text}',
                    'raw_response': response.text
                }
        
        # Return response to frontend
        if result.get('status') == 'success':
            return jsonify({
                'success': True,
                'message': result.get('message', 'Data synced successfully'),
                'details': result
            })
        else:
            return jsonify({
                'success': False,
                'error': result.get('message', 'Sync failed'),
                'details': result
            }), 400
            
    except requests.exceptions.Timeout:
        logger.error("Timeout when syncing to Google Apps Script")
        return jsonify({
            'success': False,
            'error': 'Request timeout - Google Apps Script took too long to respond'
        }), 408
        
    except requests.exceptions.ConnectionError:
        logger.error("Connection error when syncing to Google Apps Script")
        return jsonify({
            'success': False,
            'error': 'Connection error - Could not reach Google Apps Script'
        }), 503
        
    except Exception as e:
        logger.error(f"Error in sync_to_spreadsheet: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'Sync failed: {str(e)}'
        }), 500

@app.route('/api/staging/move-to-staging', methods=['POST'])
def move_to_staging():
    """Move attendance records from main data to staging."""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({
                'success': False,
                'error': 'No data provided'
            }), 400
        
        # Get filters for selecting records to move
        start_date = data.get('start_date')
        end_date = data.get('end_date')
        employee_ids = data.get('employee_ids', [])
        bus_code = data.get('bus_code')
        
        if not start_date or not end_date:
            return jsonify({
                'success': False,
                'error': 'start_date and end_date are required'
            }), 400
        
        # Get attendance data from main system
        attendance_data = reporter.get_attendance_data(start_date, end_date, bus_code)
        
        # Filter by employee IDs if specified
        if employee_ids:
            attendance_data = [record for record in attendance_data if record.get('EmployeeID') in employee_ids]
        
        if not attendance_data:
            return jsonify({
                'success': False,
                'error': 'No attendance records found for the specified criteria'
            }), 404
        
        # Convert attendance data to staging format
        staging_records = []
        for record in attendance_data:
            staging_record = {
                'employee_id': record.get('EmployeeID', ''),
                'employee_name': record.get('EmployeeName', ''),
                'date': record.get('TADate').strftime('%Y-%m-%d') if record.get('TADate') else '',
                'day_of_week': record.get('DayOfWeek', ''),
                'shift': record.get('Shift', ''),
                'check_in': record.get('TACheckIn').strftime('%H:%M') if record.get('TACheckIn') else '',
                'check_out': record.get('TACheckOut').strftime('%H:%M') if record.get('TACheckOut') else '',
                'regular_hours': float(record.get('RegularHours', 0)),
                'overtime_hours': float(record.get('OvertimeHours', 0)),
                'task_code': '',  # To be filled from charge job data
                'station_code': '',
                'machine_code': '',
                'expense_code': '',
                'source_record_id': f"{record.get('EmployeeID', '')}_{record.get('TADate').strftime('%Y%m%d') if record.get('TADate') else ''}",
                'notes': 'Moved from main attendance data'
            }
            staging_records.append(staging_record)
        
        # Add records to staging database
        conn = get_staging_db_connection()
        cursor = conn.cursor()
        
        added_records = []
        errors = []
        
        for record in staging_records:
            try:
                staging_id = str(uuid.uuid4())
                
                cursor.execute('''
                    INSERT INTO staging_attendance (
                        id, employee_id, employee_name, date, day_of_week, shift,
                        check_in, check_out, regular_hours, overtime_hours, total_hours,
                        task_code, station_code, machine_code, expense_code,
                        source_record_id, notes
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    staging_id, record['employee_id'], record['employee_name'], 
                    record['date'], record['day_of_week'], record['shift'],
                    record['check_in'], record['check_out'], 
                    record['regular_hours'], record['overtime_hours'], 
                    record['regular_hours'] + record['overtime_hours'],
                    record['task_code'], record['station_code'], 
                    record['machine_code'], record['expense_code'],
                    record['source_record_id'], record['notes']
                ))
                
                added_records.append(staging_id)
                
            except Exception as record_error:
                errors.append({
                    'record': record,
                    'error': str(record_error)
                })
                continue
        
        conn.commit()
        conn.close()
        
        return jsonify({
            'success': True,
            'message': f'Successfully moved {len(added_records)} records to staging',
            'moved_records': len(added_records),
            'record_ids': added_records,
            'errors': errors,
            'source_records': len(attendance_data)
        })
        
    except Exception as e:
        logger.error(f"Error moving records to staging: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'Failed to move records to staging: {str(e)}'
        }), 500

if __name__ == '__main__':
    # Create templates directory if it doesn't exist
    os.makedirs('templates', exist_ok=True)
    os.makedirs('static', exist_ok=True)

    # Get server configuration
    server_config = config.get("server_config", {})
    port = server_config.get("port", 5173)
    host = server_config.get("host", "0.0.0.0")
    debug = server_config.get("debug", True)

    # Print startup information
    print("=" * 60)
    print("ATTENDANCE REPORT WEB APPLICATION")
    print("=" * 60)
    print(f"Current directory: {os.getcwd()}")
    print(f"Reporter status: {'OK' if reporter else 'FAILED'}")
    print(f"Templates directory: {'EXISTS' if os.path.exists('templates') else 'MISSING'}")
    print(f"Static directory: {'EXISTS' if os.path.exists('static') else 'MISSING'}")
    print(f"Staging database: {'INITIALIZED' if init_staging_database() else 'FAILED'}")
    print("=" * 60)
    print("Starting web server...")
    print(f"Access the application at: http://localhost:{port}")
    print(f"Debug endpoint available at: http://localhost:{port}/api/debug")
    print(f"Staging API available at: http://localhost:{port}/api/staging/")
    print("=" * 60)

    # Run the Flask app
    app.run(debug=debug, host=host, port=port)
