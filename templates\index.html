<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Attendance Report System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <style>
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .summary-card {
            border-left: 4px solid #667eea;
        }
        .table-container {
            max-height: 600px;
            overflow-y: auto;
        }
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
        .filter-section {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .btn-export {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            color: white;
        }
        .btn-export:hover {
            background: linear-gradient(135deg, #20c997 0%, #28a745 100%);
            color: white;
        }
        .month-card {
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }
        .month-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            border-color: #667eea;
        }
        .month-card.selected {
            border-color: #667eea;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        }
        .month-badge {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 20px;
            padding: 5px 15px;
            font-size: 0.9em;
        }

        /* Grid table styling */
        .table-container {
            overflow-x: auto;
            max-height: 600px;
        }

        .attendance-grid {
            font-size: 0.85rem;
        }

        .attendance-grid th,
        .attendance-grid td {
            padding: 0.3rem !important;
            text-align: center;
            vertical-align: middle;
            border: 1px solid #dee2e6;
        }

        .attendance-grid th:nth-child(1),
        .attendance-grid th:nth-child(2),
        .attendance-grid th:nth-child(3),
        .attendance-grid td:nth-child(1),
        .attendance-grid td:nth-child(2),
        .attendance-grid td:nth-child(3) {
            position: sticky;
            left: 0;
            background-color: #f8f9fa;
            z-index: 10;
        }

        .attendance-grid th:nth-child(1),
        .attendance-grid td:nth-child(1) {
            left: 0;
            min-width: 50px;
        }

        .attendance-grid th:nth-child(2),
        .attendance-grid td:nth-child(2) {
            left: 50px;
            min-width: 120px;
        }

        .attendance-grid th:nth-child(3),
        .attendance-grid td:nth-child(3) {
            left: 170px;
            min-width: 200px;
            text-align: left !important;
        }

        .attendance-grid .table-success {
            background-color: #d1e7dd !important;
            color: #0f5132;
            font-weight: bold;
        }

        .attendance-grid .table-danger {
            background-color: #f8d7da !important;
            color: #842029;
            font-weight: bold;
        }

        .attendance-grid .table-warning {
            background-color: #fff3cd !important;
            color: #664d03;
            font-weight: bold;
        }

        .attendance-grid .table-secondary {
            background-color: #e2e3e5 !important;
            color: #41464b;
        }

        .attendance-grid .table-info {
            background-color: #d1ecf1 !important;
            color: #055160;
        }

        /* Working hours styling */
        .attendance-grid td {
            font-family: 'Courier New', monospace;
            font-size: 0.8rem;
        }

        /* Color coding for different hour ranges */
        .hours-full {
            background-color: #d1e7dd !important;
            color: #0f5132;
            font-weight: bold;
        }

        .hours-partial {
            background-color: #f8d7da !important;
            color: #842029;
            font-weight: bold;
        }

        .hours-overtime {
            background-color: #fff3cd !important;
            color: #664d03;
            font-weight: bold;
        }

        .hours-off {
            background-color: #d1ecf1 !important;
            color: #055160;
            font-style: italic;
        }

        .hours-absent {
            background-color: #f8f9fa !important;
            color: #6c757d;
        }

        /* Enhanced visual indicators for incomplete records */
        .hours-check-in-only {
            background-color: #bbdefb !important; /* Blue background */
            color: #1976d2 !important;
            font-weight: bold;
            border: 2px solid #2196f3 !important;
        }

        .hours-check-out-only {
            background-color: #e1bee7 !important; /* Purple background */
            color: #7b1fa2 !important;
            font-weight: bold;
            border: 2px solid #9c27b0 !important;
        }

        /* Enhanced color scheme for complete records */
        .hours-dark-green {
            background-color: #2e7d32 !important; /* Dark green: >7 hours with complete record */
            color: #ffffff !important;
            font-weight: bold;
            border: 2px solid #1b5e20 !important;
        }

        .hours-light-green {
            background-color: #c8e6c9 !important; /* Light green: normal hours, no overtime */
            color: #2e7d32 !important;
            font-weight: bold;
        }

        /* New status: Overtime-only hours (no normal work hours) */
        .hours-overtime-only {
            background-color: #fff3cd !important; /* Yellow background */
            color: #664d03 !important;
            font-weight: bold;
            border: 2px solid #ffc107 !important;
        }

        /* Sync mode styles */
        .sync-mode-active {
            background-color: #fff3cd !important;
            border: 2px solid #ffc107 !important;
        }

        .sync-mode-active .card-header {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%) !important;
        }

        .row-selected {
            background-color: #cce5ff !important;
        }

        .row-selected td {
            background-color: #cce5ff !important;
        }

        .sync-status {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1050;
            min-width: 300px;
        }

        .btn-sync-active {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%) !important;
            border: none;
            color: white;
        }

        /* Monthly Grid Sync Mode Styles */
        .monthly-grid-sync-active {
            background-color: #fff3cd !important;
            border: 2px solid #ffc107 !important;
        }

        .monthly-grid-sync-active .card-header {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%) !important;
        }

        .monthly-grid-row-selectable {
            cursor: pointer;
            transition: background-color 0.2s ease;
        }

        .monthly-grid-row-selectable:hover {
            background-color: #f8f9fa !important;
        }

        .monthly-grid-row-selected {
            background-color: #cce5ff !important;
        }

        .monthly-grid-row-selected td {
            background-color: #cce5ff !important;
        }

        .monthly-grid-row-number {
            background-color: #6c757d;
            color: white;
            border-radius: 4px;
            padding: 2px 6px;
            cursor: pointer;
            user-select: none;
            transition: all 0.2s ease;
        }

        .monthly-grid-row-number:hover {
            background-color: #495057;
            transform: scale(1.1);
        }

        .monthly-grid-row-number.selected {
            background-color: #0d6efd;
            box-shadow: 0 0 0 2px rgba(13, 110, 253, 0.25);
        }

        .monthly-grid-checkbox {
            width: 16px;
            height: 16px;
            margin: 0;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h1 class="mb-0">
                            <i class="fas fa-clock me-2"></i>
                            Attendance Report System
                        </h1>
                        <p class="mb-0">VenusHR14 Database - Enhanced Attendance Reporting</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Navigation Tabs -->
        <div class="row mb-4">
            <div class="col-12">
                <ul class="nav nav-tabs" id="reportTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="monthly-tab" data-bs-toggle="tab" data-bs-target="#monthly" type="button" role="tab">
                            <i class="fas fa-calendar-alt me-2"></i>
                            Monthly Reports
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="custom-tab" data-bs-toggle="tab" data-bs-target="#custom" type="button" role="tab">
                            <i class="fas fa-filter me-2"></i>
                            Custom Date Range
                        </button>
                    </li>
                </ul>
            </div>
        </div>

        <!-- Tab Content -->
        <div class="tab-content" id="reportTabsContent">
            <!-- Monthly Reports Tab -->
            <div class="tab-pane fade show active" id="monthly" role="tabpanel">
                <!-- Monthly Filter Section -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="filter-section">
                            <h5 class="mb-3">
                                <i class="fas fa-calendar-alt me-2"></i>
                                Select Month to View Report (Business Code: PTRJ)
                            </h5>
                            <div class="row">
                                <div class="col-md-12">
                                    <small class="text-muted">Available months will be loaded automatically</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Available Months Grid -->
                <div class="row mb-4" id="monthsGrid">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="fas fa-calendar me-2"></i>
                                    Available Months (Click to View Report)
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row" id="monthsContainer">
                                    <div class="col-12 text-center">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">Loading...</span>
                                        </div>
                                        <p class="mt-2 text-muted">Loading available months...</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Monthly Summary Section -->
                <div class="row mb-4" id="monthlySummarySection" style="display: none;">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0" id="monthlyReportTitle">
                                    <i class="fas fa-chart-bar me-2"></i>
                                    Monthly Summary
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-2">
                                        <div class="card summary-card">
                                            <div class="card-body text-center">
                                                <h6 class="card-title">Employees</h6>
                                                <h4 class="text-primary" id="monthlyTotalEmployees">0</h4>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="card summary-card">
                                            <div class="card-body text-center">
                                                <h6 class="card-title">Records</h6>
                                                <h4 class="text-info" id="monthlyTotalRecords">0</h4>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="card summary-card">
                                            <div class="card-body text-center">
                                                <h6 class="card-title">Working Days</h6>
                                                <h4 class="text-success" id="monthlyWorkingDays">0</h4>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="card summary-card">
                                            <div class="card-body text-center">
                                                <h6 class="card-title">Regular Hours</h6>
                                                <h4 class="text-success" id="monthlyRegularHours">0</h4>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="card summary-card">
                                            <div class="card-body text-center">
                                                <h6 class="card-title">Overtime Hours</h6>
                                                <h4 class="text-warning" id="monthlyOvertimeHours">0</h4>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="card summary-card">
                                            <div class="card-body text-center">
                                                <button class="btn btn-export btn-sm" id="exportMonthlyBtn">
                                                    <i class="fas fa-download me-1"></i>
                                                    Export
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Custom Date Range Tab -->
            <div class="tab-pane fade" id="custom" role="tabpanel">
                <!-- Custom Filters Section -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="filter-section">
                            <h5 class="mb-3">
                                <i class="fas fa-filter me-2"></i>
                                Custom Date Range Filters
                            </h5>
                            <form id="filterForm">
                                <div class="row">
                                    <div class="col-md-3">
                                        <label for="startDate" class="form-label">Start Date</label>
                                        <input type="date" class="form-control" id="startDate" required>
                                    </div>
                                    <div class="col-md-3">
                                        <label for="endDate" class="form-label">End Date</label>
                                        <input type="date" class="form-control" id="endDate" required>
                                    </div>
                                    <div class="col-md-2">
                                        <label for="busCode" class="form-label">Business Code</label>
                                        <input type="text" class="form-control" id="busCode" placeholder="e.g., PTRJ">
                                    </div>
                                    <div class="col-md-2">
                                        <label for="employeeSelect" class="form-label">Employee</label>
                                        <select class="form-select" id="employeeSelect">
                                            <option value="">All Employees</option>
                                        </select>
                                    </div>
                                    <div class="col-md-2">
                                        <label for="shiftSelect" class="form-label">Shift</label>
                                        <select class="form-select" id="shiftSelect">
                                            <option value="">All Shifts</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="row mt-3">
                                    <div class="col-md-8">
                                        <label for="syncUrl" class="form-label">Google Apps Script URL (for sync)</label>
                                        <input type="url" class="form-control" id="syncUrl" placeholder="https://script.google.com/macros/s/YOUR_SCRIPT_ID/exec"
                                               value="https://script.google.com/macros/s/AKfycbxaf8IKT4gkAv81fS0w9-901cRyKW4F-jvQ9E6jOMm8JpIkt0bmzlp9n3S3rdUN9Hcn/exec">
                                        <small class="form-text text-muted">Enter your Google Apps Script deployment URL for syncing data</small>
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">&nbsp;</label>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="enableSync">
                                            <label class="form-check-label" for="enableSync">
                                                Enable sync functionality
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <div class="row mt-3">
                                    <div class="col-12">
                                        <button type="submit" class="btn btn-primary me-2">
                                            <i class="fas fa-search me-1"></i>
                                            Generate Report
                                        </button>
                                        <button type="button" class="btn btn-export me-2" id="exportBtn">
                                            <i class="fas fa-download me-1"></i>
                                            Export to Excel
                                        </button>
                                        <button type="button" class="btn btn-info me-2" id="exportJsonBtn">
                                            <i class="fas fa-file-code me-1"></i>
                                            Export to JSON
                                        </button>
                                        <button type="button" class="btn btn-warning me-2" id="toggleSyncMode">
                                            <i class="fas fa-sync me-1"></i>
                                            Sync Mode
                                        </button>
                                        <button type="button" class="btn btn-success me-2" id="syncToSheetBtn" style="display: none;">
                                            <i class="fas fa-cloud-upload-alt me-1"></i>
                                            Sync to Spreadsheet
                                        </button>
                                        <button type="button" class="btn btn-secondary" id="clearBtn">
                                            <i class="fas fa-times me-1"></i>
                                            Clear Filters
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Custom Summary Cards -->
                <div class="row mb-4" id="summarySection" style="display: none;">
                    <div class="col-md-2">
                        <div class="card summary-card">
                            <div class="card-body text-center">
                                <h5 class="card-title">Total Employees</h5>
                                <h3 class="text-primary" id="totalEmployees">0</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card summary-card">
                            <div class="card-body text-center">
                                <h5 class="card-title">Total Records</h5>
                                <h3 class="text-info" id="totalRecords">0</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card summary-card">
                            <div class="card-body text-center">
                                <h5 class="card-title">Regular Hours</h5>
                                <h3 class="text-success" id="totalRegularHours">0</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card summary-card">
                            <div class="card-body text-center">
                                <h5 class="card-title">Overtime Hours</h5>
                                <h3 class="text-warning" id="totalOvertimeHours">0</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card summary-card">
                            <div class="card-body text-center">
                                <h5 class="card-title">Avg Regular</h5>
                                <h3 class="text-success" id="avgRegularHours">0</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card summary-card">
                            <div class="card-body text-center">
                                <h5 class="card-title">Avg Overtime</h5>
                                <h3 class="text-warning" id="avgOvertimeHours">0</h3>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Loading Indicator -->
        <div class="loading" id="loadingIndicator">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2">Loading attendance data...</p>
        </div>

        <!-- Monthly Grid Sync Controls -->
        <div class="row mb-4" id="monthlyGridSyncControls" style="display: none;">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-cog me-2"></i>
                            Monthly Grid Sync Configuration
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8">
                                <label for="monthlyGridSyncUrl" class="form-label">Google Apps Script URL (for sync)</label>
                                <input type="url" class="form-control" id="monthlyGridSyncUrl"
                                       placeholder="https://script.google.com/macros/s/YOUR_SCRIPT_ID/exec"
                                       value="https://script.google.com/macros/s/AKfycbxaf8IKT4gkAv81fS0w9-901cRyKW4F-jvQ9E6jOMm8JpIkt0bmzlp9n3S3rdUN9Hcn/exec">
                                <small class="form-text text-muted">Enter your Google Apps Script deployment URL for syncing grid data</small>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">&nbsp;</label>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="enableMonthlyGridSync">
                                    <label class="form-check-label" for="enableMonthlyGridSync">
                                        Enable grid sync functionality
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="row mt-3" id="monthlyGridSyncActions" style="display: none;">
                            <div class="col-md-8">
                                <div class="alert alert-info mb-0">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <span id="monthlyGridSelectionInfo">No rows selected</span>
                                    <span class="ms-3" id="monthlyGridSelectionDetails"></span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="d-grid">
                                    <button type="button" class="btn btn-success" id="syncMonthlyGridToSheetBtn" disabled>
                                        <i class="fas fa-cloud-upload-alt me-1"></i>
                                        Upload Selected Data
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Results Table -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-table me-2"></i>
                            Attendance Report Results
                        </h5>
                        <!-- Monthly Grid Sync Toggle -->
                        <div id="monthlyGridSyncToggleContainer" style="display: none;">
                            <button type="button" class="btn btn-warning btn-sm" id="toggleMonthlyGridSyncModeHeader">
                                <i class="fas fa-sync me-1"></i>
                                Sync Mode
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-container">
                            <table class="table table-striped table-hover" id="attendanceTable">
                                <thead class="table-dark">
                                    <tr>
                                        <th style="width: 50px;">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="selectAll" style="display: none;">
                                                <label class="form-check-label" for="selectAll" id="selectAllLabel" style="display: none;">
                                                    All
                                                </label>
                                            </div>
                                        </th>
                                        <th>Employee ID</th>
                                        <th>Employee Name</th>
                                        <th>Date</th>
                                        <th>Day</th>
                                        <th>Shift</th>
                                        <th>Check In</th>
                                        <th>Check Out</th>
                                        <th>Regular Hours</th>
                                        <th>Overtime Hours</th>
                                        <th>Total Hours</th>
                                    </tr>
                                </thead>
                                <tbody id="attendanceTableBody">
                                    <tr>
                                        <td colspan="11" class="text-center text-muted">
                                            <i class="fas fa-info-circle me-2"></i>
                                            Please select date range and click "Generate Report" to view attendance data
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- DataTables - Load conditionally -->
    <script>
        // Check if we want to use DataTables
        const useDataTables = true; // Set to false to disable DataTables

        if (useDataTables) {
            // Load DataTables scripts
            const script1 = document.createElement('script');
            script1.src = 'https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js';
            document.head.appendChild(script1);

            const script2 = document.createElement('script');
            script2.src = 'https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js';
            document.head.appendChild(script2);
        }
    </script>
    <script src="{{ url_for('static', filename='app.js') }}"></script>
</body>
</html>
