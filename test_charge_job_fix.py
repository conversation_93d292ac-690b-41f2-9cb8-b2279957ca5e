#!/usr/bin/env python3
"""
Test script to verify the charge job endpoint fix and parsing functionality.
This script tests the new charge job data parsing and API endpoint.
"""

import requests
import json
import sys
from datetime import datetime

# Test configuration
FLASK_APP_URL = "http://localhost:5173"
CHARGE_JOB_API_URL = "https://script.google.com/macros/s/AKfycbxy72FcKPhhuTJ3qT_DhJCLI8Z_xk9NmQlZ4mdmmtdZ-HDTHM8ER2RpYk40W--rmKjQ/exec"

def test_direct_charge_job_api():
    """Test the Google Apps Script charge job API directly."""
    print("=" * 70)
    print("TESTING DIRECT CHARGE JOB API")
    print("=" * 70)
    
    try:
        print(f"Fetching data from: {CHARGE_JOB_API_URL}")
        response = requests.get(CHARGE_JOB_API_URL, timeout=30)
        
        print(f"Status: {response.status_code}")
        print(f"Response Length: {len(response.text)} characters")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"✅ JSON Response received")
                print(f"Response Type: {type(data)}")
                
                if isinstance(data, list):
                    print(f"Records Count: {len(data)}")
                    if len(data) > 0:
                        print("\nSample Record:")
                        sample = data[0]
                        print(json.dumps(sample, indent=2))
                        
                        # Check for charge job field
                        charge_job_fields = ['chargeJob', 'charge_job', 'ChargeJob', 'task_code_data']
                        found_field = None
                        for field in charge_job_fields:
                            if field in sample:
                                found_field = field
                                break
                        
                        if found_field:
                            print(f"\n✅ Found charge job field: '{found_field}'")
                            sample_charge_job = sample[found_field]
                            print(f"Sample charge job data: '{sample_charge_job}'")
                            
                            # Test parsing
                            if ' / ' in sample_charge_job:
                                parts = sample_charge_job.split(' / ')
                                print(f"Parse test - Found {len(parts)} components:")
                                for i, part in enumerate(parts):
                                    print(f"  Component {i+1}: '{part}'")
                            else:
                                print("⚠️  No ' / ' separator found in charge job data")
                        else:
                            print("❌ No charge job field found in sample record")
                            print("Available fields:", list(sample.keys()))
                            
                elif isinstance(data, dict):
                    print(f"Dict Response - Keys: {list(data.keys())}")
                    if 'data' in data:
                        inner_data = data['data']
                        print(f"Inner data type: {type(inner_data)}")
                        if isinstance(inner_data, list):
                            print(f"Inner records count: {len(inner_data)}")
                else:
                    print(f"Unexpected response format: {type(data)}")
                    
                return True
                
            except json.JSONDecodeError as e:
                print(f"❌ JSON Parse Error: {e}")
                print("Response preview:")
                print(response.text[:500])
                return False
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print("Response:")
            print(response.text[:500])
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_flask_charge_job_endpoint():
    """Test the Flask charge job endpoint."""
    print("\n" + "=" * 70)
    print("TESTING FLASK CHARGE JOB ENDPOINT")
    print("=" * 70)
    
    try:
        print(f"Testing Flask endpoint: {FLASK_APP_URL}/api/employee-charge-jobs")
        response = requests.get(f"{FLASK_APP_URL}/api/employee-charge-jobs", timeout=30)
        
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print("✅ Flask endpoint successful!")
                
                print(f"Success: {result.get('success')}")
                print(f"Total Records: {result.get('total_records')}")
                print(f"Source URL: {result.get('source_url')}")
                
                if 'format_statistics' in result:
                    print("\nFormat Statistics:")
                    for format_type, count in result['format_statistics'].items():
                        print(f"  {format_type}: {count} records")
                
                if 'parse_errors' in result:
                    print(f"\nParse Errors: {result['parse_errors']}")
                
                data = result.get('data', {})
                if data:
                    print(f"\nProcessed Employee Records: {len(data)}")
                    
                    # Show a sample parsed record
                    sample_key = next(iter(data))
                    sample_record = data[sample_key]
                    print(f"\nSample Parsed Record (Key: {sample_key}):")
                    print(f"  Employee Name: {sample_record.get('employee_name')}")
                    print(f"  Employee ID: {sample_record.get('employee_id')}")
                    print(f"  Task Code: {sample_record.get('task_code')}")
                    print(f"  Station Code: {sample_record.get('station_code')}")
                    print(f"  Machine Code: {sample_record.get('machine_code')}")
                    print(f"  Expense Code: {sample_record.get('expense_code')}")
                    print(f"  Format Type: {sample_record.get('format_type')}")
                    print(f"  Raw Charge Job: {sample_record.get('raw_charge_job')}")
                    
                    if sample_record.get('parse_error'):
                        print(f"  Parse Error: {sample_record.get('parse_error')}")
                else:
                    print("❌ No employee data returned")
                    
                return True
                
            except json.JSONDecodeError as e:
                print(f"❌ JSON Parse Error: {e}")
                print("Response preview:")
                print(response.text[:500])
                return False
                
        elif response.status_code == 500:
            print("❌ Internal Server Error (500)")
            try:
                error_data = response.json()
                print("Error details:")
                print(json.dumps(error_data, indent=2))
            except:
                print("Error response (non-JSON):")
                print(response.text[:500])
            return False
            
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print("Response:")
            print(response.text[:500])
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection Error: Could not connect to Flask app")
        print("Make sure the Flask app is running on http://localhost:5173")
        return False
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_charge_job_parsing():
    """Test the charge job parsing logic with sample data."""
    print("\n" + "=" * 70)
    print("TESTING CHARGE JOB PARSING LOGIC")
    print("=" * 70)
    
    # Test cases
    test_cases = [
        {
            'name': '4-component format',
            'input': '(OC7110) FRUIT RECEPTION AND STORAGE / STN-FRC (STATION FRUIT RECEPTION) / FRC00000 (LABOUR COST) / L (LABOUR)',
            'expected_components': 4
        },
        {
            'name': '3-component format',
            'input': 'TSK001 / STN-001 / EXP001',
            'expected_components': 3
        },
        {
            'name': 'Empty string',
            'input': '',
            'expected_components': 0
        },
        {
            'name': 'Single component',
            'input': 'TSK001',
            'expected_components': 1
        }
    ]
    
    for test_case in test_cases:
        print(f"\nTest: {test_case['name']}")
        print(f"Input: '{test_case['input']}'")
        
        if test_case['input']:
            parts = test_case['input'].split(' / ')
            parts = [part.strip() for part in parts if part.strip()]
            print(f"Parsed Components ({len(parts)}):")
            for i, part in enumerate(parts):
                print(f"  {i+1}: '{part}'")
                
            if len(parts) == test_case['expected_components']:
                print("✅ Expected component count matches")
            else:
                print(f"⚠️  Expected {test_case['expected_components']}, got {len(parts)}")
        else:
            print("Empty input - no parsing needed")

if __name__ == "__main__":
    print("Starting charge job functionality tests...")
    print(f"Timestamp: {datetime.now().isoformat()}")
    
    # Test 1: Direct Google Apps Script API
    direct_api_success = test_direct_charge_job_api()
    
    # Test 2: Charge job parsing logic
    test_charge_job_parsing()
    
    # Test 3: Flask endpoint
    flask_success = test_flask_charge_job_endpoint()
    
    print("\n" + "=" * 70)
    print("TEST SUMMARY")
    print("=" * 70)
    print(f"Direct API test: {'✅ PASS' if direct_api_success else '❌ FAIL'}")
    print(f"Flask endpoint test: {'✅ PASS' if flask_success else '❌ FAIL'}")
    
    if direct_api_success and flask_success:
        print("\n🎉 ALL TESTS PASSED!")
        print("The charge job functionality fix is working correctly.")
        sys.exit(0)
    else:
        print("\n❌ SOME TESTS FAILED")
        print("Please check the error messages above for troubleshooting.")
        sys.exit(1) 