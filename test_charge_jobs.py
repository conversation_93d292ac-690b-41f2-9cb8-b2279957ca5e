#!/usr/bin/env python3
"""
Test script to diagnose the employee charge jobs endpoint issue.
"""

import requests
import json
import sys

# Test configuration
GOOGLE_APPS_SCRIPT_URL = "https://script.google.com/macros/s/AKfycbxaf8IKT4gkAv81fS0w9-901cRyKW4F-jvQ9E6jOMm8JpIkt0bmzlp9n3S3rdUN9Hcn/exec"

def test_google_apps_script_direct():
    """Test direct call to Google Apps Script to see what data is available."""
    print("=" * 60)
    print("TESTING GOOGLE APPS SCRIPT DIRECT CALL")
    print("=" * 60)
    
    print(f"Testing URL: {GOOGLE_APPS_SCRIPT_URL}")
    
    # Test 1: Simple GET request (like the current endpoint does)
    print("\n1. Testing simple GET request (current method)...")
    try:
        response = requests.get(GOOGLE_APPS_SCRIPT_URL, timeout=30)
        print(f"Status: {response.status_code}")
        print(f"Content-Type: {response.headers.get('content-type', 'unknown')}")
        print(f"Response length: {len(response.text)}")
        print(f"First 500 chars: {response.text[:500]}")
        
        # Try to parse as JSON
        try:
            data = response.json()
            print(f"✅ Valid JSON response with {len(data)} items")
            
            # Check structure
            if len(data) > 0:
                sample = data[0]
                print(f"Sample record keys: {list(sample.keys())}")
                print(f"Sample record: {sample}")
        except:
            print("❌ Response is not valid JSON")
            
    except Exception as e:
        print(f"❌ Error: {e}")
    
    # Test 2: With get_data action
    print("\n2. Testing with 'get_data' action...")
    try:
        params = {'action': 'get_data'}
        response = requests.get(GOOGLE_APPS_SCRIPT_URL, params=params, timeout=30)
        print(f"Status: {response.status_code}")
        print(f"Response: {response.text[:500]}")
        
        try:
            data = response.json()
            print(f"✅ Valid JSON response: {data}")
        except:
            print("❌ Response is not valid JSON")
            
    except Exception as e:
        print(f"❌ Error: {e}")
    
    # Test 3: With get_employees action
    print("\n3. Testing with 'get_employees' action...")
    try:
        params = {'action': 'get_employees'}
        response = requests.get(GOOGLE_APPS_SCRIPT_URL, params=params, timeout=30)
        print(f"Status: {response.status_code}")
        print(f"Response: {response.text[:500]}")
        
        try:
            data = response.json()
            print(f"✅ Valid JSON response: {data}")
        except:
            print("❌ Response is not valid JSON")
            
    except Exception as e:
        print(f"❌ Error: {e}")

def test_flask_endpoint():
    """Test the Flask endpoint to see the exact error."""
    print("\n" + "=" * 60)
    print("TESTING FLASK EMPLOYEE CHARGE JOBS ENDPOINT")
    print("=" * 60)
    
    try:
        response = requests.get("http://localhost:5000/api/employee-charge-jobs", timeout=30)
        print(f"Status: {response.status_code}")
        print(f"Content-Type: {response.headers.get('content-type', 'unknown')}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"✅ Success! Got {data.get('total_records', 0)} employee records")
            except:
                print("❌ Response is not valid JSON")
        else:
            print(f"❌ HTTP Error {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to Flask app. Make sure it's running on http://localhost:5000")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    
    return response.status_code == 200

if __name__ == "__main__":
    print("Starting employee charge jobs diagnosis...")
    
    # Test Google Apps Script directly first
    test_google_apps_script_direct()
    
    # Test Flask endpoint
    flask_success = test_flask_endpoint()
    
    print("\n" + "=" * 60)
    print("DIAGNOSIS SUMMARY")
    print("=" * 60)
    print("Check the test results above to identify the issue.")
    print("Common causes:")
    print("1. Google Apps Script doesn't support employee data retrieval")
    print("2. Wrong action parameter or missing action")
    print("3. Google Apps Script returns HTML instead of JSON")
    print("4. Network/timeout issues")
    print("=" * 60) 