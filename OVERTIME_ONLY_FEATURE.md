# Overtime-Only Feature Implementation

## Overview
Added a new yellow background color status for attendance cells where employees have only overtime hours but no normal work hours for a particular day.

## Color Coding System (Updated)

| Status | Background Color | Description | Use Case |
|--------|------------------|-------------|----------|
| **Dark Green** | `#2e7d32` | Normal hours >7 with complete record | Full day with overtime |
| **Light Green** | `#c8e6c9` | Normal hours without overtime | Regular working day |
| **Blue** | `#bbdefb` | Only check-in (missing check-out) | Incomplete record - missing checkout |
| **Purple** | `#e1bee7` | Only check-out (missing check-in) | Incomplete record - missing checkin |
| **Yellow** | `#fff3cd` | **NEW: Overtime-only hours** | **Special shifts with only overtime** |

## Implementation Details

### Backend Logic (`src/attendance_reporter.py`)
The system already had logic to detect overtime-only records:

```python
elif not has_check_in and not has_check_out:
    # Check if there's overtime on a day without attendance
    if overtime_hours > 0:
        overtime_display = f"{overtime_hours:.1f}".rstrip('0').rstrip('.')
        display = f"(0) | ({overtime_display})"
        return {
            'display': display,
            'regular': 0,
            'overtime': overtime_hours,
            'status': 'overtime_only',  # <-- This status triggers yellow color
            'check_in_only': False,
            'check_out_only': False,
            'complete_record': False
        }
```

### Frontend Styling (`templates/index.html`)
Added new CSS class:

```css
.hours-overtime-only {
    background-color: #fff3cd !important; /* Yellow background */
    color: #664d03 !important;
    font-weight: bold;
    border: 2px solid #ffc107 !important;
}
```

### Frontend Logic (`static/app.js`)
Enhanced the `getWorkingHoursClass()` function:

```javascript
// Handle overtime-only records (new status)
if (status === 'overtime_only') {
    return 'hours-overtime-only'; // Yellow background
}

// Fallback detection from display format
if (parsedRegularHours === 0 && parsedOvertimeHours > 0) {
    return 'hours-overtime-only'; // Yellow background for overtime-only
}
```

### Google Apps Script (`google_apps_script.js`)
Added conditional formatting:

```javascript
} else if (dayStatus?.status === 'overtime_only' || (regularHours === 0 && overtimeHours > 0)) {
  // NEW: Yellow background for overtime-only records
  cell.setBackground('#fff3cd'); // Yellow
  cell.setFontColor('#664d03');
  cell.setFontWeight('bold');
  cell.setBorder(true, true, true, true, false, false, '#ffc107', SpreadsheetApp.BorderStyle.SOLID_MEDIUM);
}
```

## Use Cases

### When Yellow Background is Applied:
1. **Special Overtime Shifts**: Employee worked only overtime hours (e.g., night shift, weekend special duty)
2. **Emergency Call-ins**: Employee called in for overtime work outside normal hours
3. **Part-time Overtime**: Employees who don't have regular hours but work overtime
4. **Project-based Work**: Special project work that's classified as overtime only

### Data Requirements:
- `OTHourDuration > 0` in HR_T_Overtime table
- No check-in/check-out times in HR_T_TAMachine_Summary
- Display format: `(0) | (X)` where X is the overtime hours

## Testing

Successfully tested with:
- Backend logic correctly identifies overtime-only records
- Status `'overtime_only'` properly assigned
- Display format `(0) | (3)` for 3 hours of overtime-only work
- Visual indicators ready for frontend and Google Sheets

## Benefits

1. **Clear Visual Distinction**: Yellow color immediately identifies special overtime-only shifts
2. **Data Quality**: Helps distinguish between absent employees and those working special overtime shifts  
3. **Payroll Accuracy**: Ensures overtime-only work is properly highlighted for payroll processing
4. **Audit Trail**: Makes it easy to identify and verify special work arrangements

## Example Display

```
Employee A: (7) | (-)    # Light green - normal work only
Employee B: (7) | (2)    # Dark green - normal + overtime  
Employee C: (0) | (4)    # YELLOW - overtime only (NEW!)
Employee D: -            # Gray - absent
Employee E: (7) | (-)    # Blue - check-in only (incomplete)
```

This enhancement provides better visibility into different types of work patterns and ensures that overtime-only shifts are properly highlighted in the attendance reporting system. 