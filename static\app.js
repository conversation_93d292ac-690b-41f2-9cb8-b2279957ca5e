/**
 * Attendance Report System - Frontend JavaScript
 * Handles user interactions, API calls, and data display
 */

// Global variables
let currentSelectedMonth = null;
let availableMonths = [];
let syncModeActive = false;
let selectedRows = new Set();
// Monthly Grid Sync Variables
let monthlyGridSyncModeActive = false;
let monthlyGridSelectedRows = new Set();
let currentMonthlyGridData = null;
// Employee Charge Job Data
let employeeChargeJobData = null;

$(document).ready(function() {
    console.log('Document ready, initializing application...');

    // Initialize the application
    initializeApp();

    // Set default dates (last 7 days)
    setDefaultDates();

    // Load initial data for custom tab
    loadEmployees();
    loadShifts();

    // Auto-load available months on page load
    console.log('Auto-loading available months...');
    loadAvailableMonths();
    
    // Load employee charge job data
    console.log('Loading employee charge job data...');
    loadEmployeeChargeJobData()
        .then(() => {
            console.log('Employee charge job data loaded successfully');
        })
        .catch((error) => {
            console.warn('Could not load employee charge job data during initialization:', error);
        });
});

/**
 * Initialize the application
 */
function initializeApp() {
    console.log('Initializing application...');

    // Don't initialize DataTable immediately - wait for data
    // We'll initialize it in displayAttendanceData function

    // Bind event handlers
    $('#filterForm').on('submit', handleFormSubmit);
    $('#exportBtn').on('click', handleExport);
    $('#exportJsonBtn').on('click', handleJsonExport);
    $('#toggleSyncMode').on('click', toggleSyncMode);
    $('#syncToSheetBtn').on('click', syncToSpreadsheet);
    $('#selectAll').on('change', handleSelectAll);
    $('#clearBtn').on('click', clearFilters);
    $('#busCode').on('change', function() {
        loadEmployees();
        loadShifts();
    });

    // Monthly tab event handlers
    $('#busCodeMonthly').on('change', loadAvailableMonths);
    $('#exportMonthlyBtn').on('click', handleMonthlyExport);

    // Monthly Grid Sync event handlers
    $('#toggleMonthlyGridSyncMode').on('click', toggleMonthlyGridSyncMode);
    $('#toggleMonthlyGridSyncModeHeader').on('click', toggleMonthlyGridSyncMode);
    $('#syncMonthlyGridToSheetBtn').on('click', syncMonthlyGridToSpreadsheet);

    console.log('Application initialized');
}

/**
 * Set default date range (last 7 days)
 */
function setDefaultDates() {
    const today = new Date();
    const lastWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);

    $('#endDate').val(formatDate(today));
    $('#startDate').val(formatDate(lastWeek));
}

/**
 * Format date to YYYY-MM-DD
 */
function formatDate(date) {
    return date.toISOString().split('T')[0];
}

/**
 * Handle form submission
 */
function handleFormSubmit(e) {
    e.preventDefault();

    const startDate = $('#startDate').val();
    const endDate = $('#endDate').val();

    if (!startDate || !endDate) {
        showAlert('Please select both start and end dates before exporting', 'warning');
        return;
    }

    if (new Date(startDate) > new Date(endDate)) {
        showAlert('Start date cannot be later than end date', 'warning');
        return;
    }

    loadAttendanceData();
    loadSummaryData();
}

// Global variable to track DataTable instance
let attendanceDataTable = null;

/**
 * Display attendance data in the table
 */
function displayAttendanceData(data) {
    console.log('Displaying attendance data:', data.length, 'records');

    // Destroy existing DataTable if it exists
    if (attendanceDataTable) {
        console.log('Destroying existing DataTable...');
        attendanceDataTable.destroy();
        attendanceDataTable = null;
    }

    const tableBody = $('#attendanceTableBody');
    tableBody.empty();

    if (!data || data.length === 0) {
        console.log('No data to display');
        tableBody.html(`
            <tr>
                <td colspan="11" class="text-center text-muted">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    No attendance data found for the selected criteria
                </td>
            </tr>
        `);
        return;
    }

    // Store data globally for sync functionality
    window.currentAttendanceData = data;

    // Add data rows to table body
    data.forEach(function(record, index) {
        const totalHours = (record.RegularHours + record.OvertimeHours).toFixed(2);
        const rowId = `row_${index}`;

        const checkboxHtml = syncModeActive ? 
            `<input type="checkbox" class="form-check-input row-checkbox" data-row-id="${index}" id="check_${index}">` : '';

        const row = `
            <tr id="${rowId}" data-row-index="${index}">
                <td>
                    ${checkboxHtml}
                </td>
                <td>${record.EmployeeID || ''}</td>
                <td>${record.EmployeeName || ''}</td>
                <td>${record.Date || ''}</td>
                <td>${record.DayOfWeek || ''}</td>
                <td>${record.Shift || ''}</td>
                <td>${record.CheckIn || ''}</td>
                <td>${record.CheckOut || ''}</td>
                <td>${record.RegularHours ? record.RegularHours.toFixed(2) : '0.00'}</td>
                <td>${record.OvertimeHours ? record.OvertimeHours.toFixed(2) : '0.00'}</td>
                <td>${totalHours}</td>
            </tr>
        `;
        tableBody.append(row);
    });

    // Bind row selection events if sync mode is active
    if (syncModeActive) {
        $('.row-checkbox').on('change', handleRowSelection);
        $('#attendanceTable tbody tr').on('click', function(e) {
            if (e.target.type !== 'checkbox') {
                const checkbox = $(this).find('.row-checkbox');
                checkbox.prop('checked', !checkbox.prop('checked')).trigger('change');
            }
        });
    }

    // Initialize DataTable after data is loaded (if available)
    if (typeof $.fn.DataTable !== 'undefined' && window.useDataTables !== false) {
        try {
            console.log('Initializing DataTable with', data.length, 'rows...');
            attendanceDataTable = $('#attendanceTable').DataTable({
                "pageLength": 25,
                "order": [[3, "desc"]], // Sort by date descending (adjusted for checkbox column)
                "columnDefs": [
                    { "orderable": false, "targets": [0, 6, 7] } // Disable sorting for checkbox, check in/out columns
                ],
                "destroy": true // Allow re-initialization
            });
            console.log('DataTable initialized successfully');
        } catch (error) {
            console.log('DataTable initialization failed:', error);
            console.log('Continuing without DataTable functionality');
        }
    } else {
        console.log('DataTables not available or disabled, showing plain table');
        // Add basic table styling for better UX
        $('#attendanceTable').addClass('table-responsive');
    }
}

/**
 * Load summary statistics
 */
function loadSummaryData() {
    const params = {
        start_date: $('#startDate').val(),
        end_date: $('#endDate').val(),
        bus_code: $('#busCode').val()
    };

    // Remove empty parameters
    Object.keys(params).forEach(key => {
        if (!params[key]) delete params[key];
    });

    $.ajax({
        url: '/api/summary',
        method: 'GET',
        data: params,
        success: function(response) {
            if (response.success) {
                displaySummaryData(response.data);
            }
        },
        error: function(xhr, status, error) {
            console.error('Failed to load summary data:', error);
        }
    });
}

/**
 * Display summary statistics
 */
function displaySummaryData(data) {
    $('#totalEmployees').text(data.total_employees);
    $('#totalRecords').text(data.total_records);
    $('#totalRegularHours').text(data.total_regular_hours);
    $('#totalOvertimeHours').text(data.total_overtime_hours);
    $('#avgRegularHours').text(data.avg_regular_hours);
    $('#avgOvertimeHours').text(data.avg_overtime_hours);

    $('#summarySection').show();
}

/**
 * Load employees list
 */
function loadEmployees() {
    const busCode = $('#busCode').val();
    const params = busCode ? { bus_code: busCode } : {};

    $.ajax({
        url: '/api/employees',
        method: 'GET',
        data: params,
        success: function(response) {
            if (response.success) {
                populateEmployeeSelect(response.data);
            }
        },
        error: function(xhr, status, error) {
            console.error('Failed to load employees:', error);
        }
    });
}

/**
 * Populate employee select dropdown
 */
function populateEmployeeSelect(employees) {
    const select = $('#employeeSelect');
    select.empty();
    select.append('<option value="">All Employees</option>');

    employees.forEach(function(employee) {
        select.append(`<option value="${employee.EmployeeID}">${employee.EmployeeName} (${employee.EmployeeID})</option>`);
    });
}

/**
 * Load shifts list
 */
function loadShifts() {
    const busCode = $('#busCode').val();
    const params = busCode ? { bus_code: busCode } : {};

    $.ajax({
        url: '/api/shifts',
        method: 'GET',
        data: params,
        success: function(response) {
            if (response.success) {
                populateShiftSelect(response.data);
            }
        },
        error: function(xhr, status, error) {
            console.error('Failed to load shifts:', error);
        }
    });
}

/**
 * Populate shift select dropdown
 */
function populateShiftSelect(shifts) {
    const select = $('#shiftSelect');
    select.empty();
    select.append('<option value="">All Shifts</option>');

    shifts.forEach(function(shift) {
        if (shift.Shift) {
            select.append(`<option value="${shift.Shift}">${shift.Shift}</option>`);
        }
    });
}

/**
 * Handle export to Excel
 */
function handleExport() {
    const startDate = $('#startDate').val();
    const endDate = $('#endDate').val();

    if (!startDate || !endDate) {
        showAlert('Please select both start and end dates before exporting', 'warning');
        return;
    }

    const params = {
        start_date: startDate,
        end_date: endDate,
        bus_code: $('#busCode').val(),
        employee_id: $('#employeeSelect').val(),
        shift: $('#shiftSelect').val()
    };

    // Remove empty parameters
    Object.keys(params).forEach(key => {
        if (!params[key]) delete params[key];
    });

    // Create download URL
    const queryString = new URLSearchParams(params).toString();
    const downloadUrl = `/api/export?${queryString}`;

    // Show loading state
    const exportBtn = $('#exportBtn');
    const originalText = exportBtn.html();
    exportBtn.html('<i class="fas fa-spinner fa-spin me-1"></i>Exporting...');
    exportBtn.prop('disabled', true);

    // Create temporary link and trigger download
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = '';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // Reset button state after a delay
    setTimeout(function() {
        exportBtn.html(originalText);
        exportBtn.prop('disabled', false);
        showAlert('Export initiated. Download should start shortly.', 'info');
    }, 2000);
}

/**
 * Handle JSON export for custom tab
 */
function handleJsonExport() {
    const startDate = $('#startDate').val();
    const endDate = $('#endDate').val();

    if (!startDate || !endDate) {
        showAlert('Please select both start and end dates before exporting', 'warning');
        return;
    }

    const params = {
        start_date: startDate,
        end_date: endDate,
        bus_code: $('#busCode').val(),
        employee_id: $('#employeeSelect').val(),
        shift: $('#shiftSelect').val(),
        format: 'json'
    };

    // Remove empty parameters
    Object.keys(params).forEach(key => {
        if (!params[key]) delete params[key];
    });

    // Create download URL
    const queryString = new URLSearchParams(params).toString();
    const downloadUrl = `/api/export?${queryString}`;

    // Show loading state
    const exportBtn = $('#exportJsonBtn');
    const originalText = exportBtn.html();
    exportBtn.html('<i class="fas fa-spinner fa-spin me-1"></i>Exporting...');
    exportBtn.prop('disabled', true);

    // Create temporary link and trigger download
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = '';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // Reset button state after a delay
    setTimeout(function() {
        exportBtn.html(originalText);
        exportBtn.prop('disabled', false);
        showAlert('JSON export initiated. Download should start shortly.', 'info');
    }, 2000);
}

/**
 * Clear all filters
 */
function clearFilters() {
    console.log('Clearing filters...');

    $('#filterForm')[0].reset();
    setDefaultDates();
    $('#summarySection').hide();

    // Clear table
    if (attendanceDataTable) {
        attendanceDataTable.destroy();
        attendanceDataTable = null;
    }

    // Hide monthly grid sync controls and header toggle
    $('#monthlyGridSyncControls').hide();
    $('#monthlyGridSyncToggleContainer').hide();
    
    $('#attendanceTableBody').html(`
        <tr>
            <td colspan="10" class="text-center text-muted">
                <i class="fas fa-info-circle me-2"></i>
                Please select date range and click "Generate Report" to view attendance data
            </td>
        </tr>
    `);

    // Reload dropdowns
    loadEmployees();
    loadShifts();

    showAlert('Filters cleared', 'info');
}

/**
 * Load employee charge job data from Google Apps Script API
 */
function loadEmployeeChargeJobData() {
    console.log('Loading employee charge job data...');
    
    return new Promise((resolve, reject) => {
        $.ajax({
            url: '/api/employee-charge-jobs',
            method: 'GET',
            success: function(response) {
                console.log('Employee charge job data response:', response);
                
                if (response.success) {
                    employeeChargeJobData = response.data;
                    console.log(`Loaded charge job data for ${response.total_records} employees`);
                    resolve(employeeChargeJobData);
                } else {
                    console.error('Failed to load employee charge job data:', response.error);
                    employeeChargeJobData = {};
                    reject(response.error);
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX error loading employee charge job data:', {xhr, status, error});
                employeeChargeJobData = {};
                reject(error);
            }
        });
    });
}

/**
 * Get charge job data for an employee by name
 */
function getEmployeeChargeJobData(employeeName) {
    if (!employeeChargeJobData) {
        return {
            task_code: '',
            machine_code: '',
            expense_code: ''
        };
    }
    
    // Try exact match first
    if (employeeChargeJobData[employeeName]) {
        return employeeChargeJobData[employeeName];
    }
    
    // Try case-insensitive match
    const nameUpper = employeeName.toUpperCase();
    for (const [name, data] of Object.entries(employeeChargeJobData)) {
        if (name.toUpperCase() === nameUpper) {
            return data;
        }
    }
    
    // Try partial match (for names that might be slightly different)
    for (const [name, data] of Object.entries(employeeChargeJobData)) {
        if (name.toUpperCase().includes(nameUpper) || nameUpper.includes(name.toUpperCase())) {
            return data;
        }
    }
    
    // Return empty data if no match found
    return {
        task_code: '',
        machine_code: '',
        expense_code: ''
    };
}

/**
 * Show/hide loading indicator
 */
function showLoading(show) {
    if (show) {
        $('#loadingIndicator').show();
    } else {
        $('#loadingIndicator').hide();
    }
}

/**
 * Get month name from month number
 */
function getMonthName(month) {
    const monthNames = [
        'January', 'February', 'March', 'April', 'May', 'June',
        'July', 'August', 'September', 'October', 'November', 'December'
    ];
    return monthNames[month - 1] || 'Unknown';
}

/**
 * Show alert message
 */
function showAlert(message, type) {
    // Remove existing alerts
    $('.alert').remove();

    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    $('.container-fluid').prepend(alertHtml);

    // Auto-dismiss after 5 seconds
    setTimeout(function() {
        $('.alert').alert('close');
    }, 5000);
}

/**
 * Load available months from the API
 */
function loadAvailableMonths() {
    const busCode = 'PTRJ'; // Default to PTRJ
    const params = { bus_code: busCode };

    console.log('Loading available months with params:', params);
    showLoading(true);

    $.ajax({
        url: '/api/months',
        method: 'GET',
        data: params,
        success: function(response) {
            console.log('API response:', response);

            if (response.success) {
                availableMonths = response.data;
                console.log('Available months loaded:', availableMonths.length);
                displayAvailableMonths(response.data);
                showAlert(`Loaded ${response.data.length} available months`, 'success');
            } else {
                console.error('API returned error:', response.error);
                showAlert('Error loading available months: ' + response.error, 'danger');
            }
        },
        error: function(xhr, status, error) {
            console.error('AJAX error:', {xhr, status, error});
            console.error('Response text:', xhr.responseText);

            let errorMessage = 'Failed to load available months: ' + error;
            if (xhr.responseText) {
                try {
                    const errorResponse = JSON.parse(xhr.responseText);
                    errorMessage = 'Error: ' + (errorResponse.error || error);
                } catch (e) {
                    errorMessage = 'Server error: ' + xhr.status;
                }
            }

            showAlert(errorMessage, 'danger');
        },
        complete: function() {
            showLoading(false);
        }
    });
}

/**
 * Display available months as clickable cards
 */
function displayAvailableMonths(months) {
    console.log('Displaying months:', months);
    const container = $('#monthsContainer');
    container.empty();

    if (!months || months.length === 0) {
        console.log('No months to display');
        container.html(`
            <div class="col-12">
                <div class="text-center text-muted">
                    <i class="fas fa-calendar-times fa-3x mb-3"></i>
                    <p>No months with attendance data found</p>
                    <small>Check console for debugging information</small>
                </div>
            </div>
        `);
        $('#monthsGrid').show();
        return;
    }

    console.log(`Creating ${months.length} month cards`);

    months.forEach(function(month, index) {
        console.log(`Creating card for month ${index + 1}:`, month);

        const monthCard = `
            <div class="col-md-3 col-sm-6 mb-3">
                <div class="card month-card" data-year="${month.year}" data-month="${month.month}">
                    <div class="card-body text-center">
                        <h5 class="card-title">
                            <i class="fas fa-calendar-alt me-2"></i>
                            ${month.display_name || `${month.month_name} ${month.year}`}
                        </h5>
                        <div class="mb-2">
                            <span class="month-badge">${month.record_count || 0} Records</span>
                        </div>
                        <p class="card-text">
                            <small class="text-muted">
                                ${month.employee_count || 0} Employees<br>
                                ${month.first_date || 'N/A'} to ${month.last_date || 'N/A'}
                            </small>
                        </p>
                    </div>
                </div>
            </div>
        `;
        container.append(monthCard);
    });

    console.log('Month cards created, adding click handlers...');

    // Add click handlers to month cards
    $('.month-card').on('click', function() {
        const year = $(this).data('year');
        const month = $(this).data('month');
        console.log(`Month card clicked: ${month}/${year}`);
        selectMonth(year, month, $(this));
    });

    $('#monthsGrid').show();
    console.log('Months grid displayed');
}

/**
 * Handle month selection
 */
function selectMonth(year, month, cardElement) {
    // Update visual selection
    $('.month-card').removeClass('selected');
    cardElement.addClass('selected');

    // Store current selection
    currentSelectedMonth = { year: year, month: month };

    // Show report type options
    showReportTypeOptions(year, month);
}

/**
 * Show report type selection
 */
function showReportTypeOptions(year, month) {
    const monthName = getMonthName(month);
    
    // Directly load the monthly grid with station categorization
    // No longer show report type options - go straight to the consolidated grid
    loadMonthlyGrid(year, month);
    
    // Clear previous table data while loading
    $('#attendanceTableBody').html(`
        <tr>
            <td colspan="10" class="text-center text-muted">
                <i class="fas fa-spinner fa-spin me-2"></i>
                Loading daily attendance grid with station categorization...
            </td>
        </tr>
    `);
}

/**
 * Load monthly attendance grid
 */
function loadMonthlyGrid(year, month) {
    const busCode = 'PTRJ'; // Default to PTRJ
    const params = {
        year: year,
        month: month,
        bus_code: busCode
    };

    console.log('Loading monthly grid with params:', params);
    showLoading(true);

    // First, load employee charge job data, then load the attendance grid
    loadEmployeeChargeJobData()
        .then(() => {
            console.log('Employee charge job data loaded, now loading attendance grid...');
            return loadAttendanceGridData(params);
        })
        .catch((error) => {
            console.warn('Failed to load employee charge job data, continuing with attendance grid:', error);
            showAlert('Could not load employee job codes. Grid will show without job code columns.', 'warning');
            return loadAttendanceGridData(params);
        });
}

/**
 * Load attendance grid data from API
 */
function loadAttendanceGridData(params) {
    $.ajax({
        url: '/api/monthly-grid',
        method: 'GET',
        data: params,
        success: function(response) {
            console.log('Monthly grid response:', response);

            if (response.success) {
                // Show sync controls
                $('#monthlyGridSyncControls').show();
                
                // Show sync toggle in main table header
                $('#monthlyGridSyncToggleContainer').show();
                
                // Use sync-aware display function
                if (monthlyGridSyncModeActive) {
                    displayMonthlyGridWithSync(response.data);
                } else {
                    displayMonthlyGrid(response.data);
                }
                
                showAlert(`Loaded attendance grid for ${response.data.month_name} ${response.data.year}`, 'success');
            } else {
                console.error('API returned error:', response.error);
                showAlert('Error loading monthly grid: ' + response.error, 'danger');
            }
        },
        error: function(xhr, status, error) {
            console.error('AJAX error loading monthly grid:', {xhr, status, error});
            console.error('Response text:', xhr.responseText);

            let errorMessage = 'Failed to load monthly grid: ' + error;
            if (xhr.responseText) {
                try {
                    const errorResponse = JSON.parse(xhr.responseText);
                    errorMessage = 'Error: ' + (errorResponse.error || error);
                    console.error('Parsed error response:', errorResponse);
                } catch (e) {
                    errorMessage = `Server error: ${xhr.status} - ${error}`;
                }
            }

            showAlert(errorMessage, 'danger');
        },
        complete: function() {
            showLoading(false);
        }
    });
}

/**
 * Display monthly attendance grid grouped by stations
 * REMOVED: Station categorization is now integrated into the main displayMonthlyGrid function
 */
/*
function displayMonthlyGridByStation(gridData) {
    // Function removed - station categorization integrated into main displayMonthlyGrid
}
*/

/**
 * Display monthly attendance grid
 */
function displayMonthlyGrid(gridData) {
    console.log('Displaying monthly grid:', gridData);
    
    // Store data globally for sync functionality
    currentMonthlyGridData = gridData;
    
    // Completely disable DataTables for monthly grids
    window.useDataTables = false;
    
    // Destroy any existing DataTable to prevent conflicts
    if (attendanceDataTable) {
        console.log('Destroying existing DataTable for monthly grid...');
        attendanceDataTable.destroy();
        attendanceDataTable = null;
    }

    // Update title
    $('#monthlyReportTitle').html(`
        <i class="fas fa-table me-2"></i>
        ${gridData.month_name} ${gridData.year} - Daily Attendance Grid by Station
    `);

    // Create grid table header
    let headerHtml = `
        <tr>
            <th style="min-width: 50px; position: sticky; top: 0; left: 0; background: #212529; z-index: 20;">No</th>
            <th style="min-width: 120px; position: sticky; top: 0; left: 50px; background: #212529; z-index: 20;">Employee ID</th>
            <th style="min-width: 200px; position: sticky; top: 0; left: 170px; background: #212529; z-index: 20;">Employee Name</th>
    `;

    // Add day columns with day names
    for (let day = 1; day <= gridData.days_in_month; day++) {
        const date = new Date(gridData.year, gridData.month - 1, day);
        const dayOfWeek = date.getDay(); // 0=Sunday, 6=Saturday
        const isSunday = dayOfWeek === 0;
        const dayName = getDayNameIndonesian(gridData.year, gridData.month, day);
        
        // Set background color for Sunday headers
        const backgroundColor = isSunday ? '#dc3545' : '#212529'; // Red for Sunday, dark for others
        
        headerHtml += `<th style="min-width: 80px; text-align: center; font-size: 0.8rem; position: sticky; top: 0; background: ${backgroundColor}; z-index: 10; color: white;">
            <div>${day}</div>
            <div style="font-size: 0.7rem; color: #adb5bd;">${dayName}</div>
        </th>`;
    }
    
    // Add totals columns (3 separate columns)
    headerHtml += `
        <th style="min-width: 80px; text-align: center; font-size: 0.8rem; position: sticky; top: 0; background: #198754; z-index: 10; color: white;">
            <div>DAYS</div>
            <div style="font-size: 0.7rem; color: #adb5bd;">Total</div>
        </th>
        <th style="min-width: 80px; text-align: center; font-size: 0.8rem; position: sticky; top: 0; background: #198754; z-index: 10; color: white;">
            <div>REG</div>
            <div style="font-size: 0.7rem; color: #adb5bd;">Hours</div>
        </th>
        <th style="min-width: 80px; text-align: center; font-size: 0.8rem; position: sticky; top: 0; background: #198754; z-index: 10; color: white;">
            <div>OT</div>
            <div style="font-size: 0.7rem; color: #adb5bd;">Hours</div>
        </th>
    `;

    // Add charge job columns (Task Code, Machine Code, Expense Code)
    headerHtml += `
        <th style="min-width: 120px; text-align: center; font-size: 0.8rem; position: sticky; top: 0; background: #0d6efd; z-index: 10; color: white;">
            <div>TASK</div>
            <div style="font-size: 0.7rem; color: #adb5bd;">Code</div>
        </th>
        <th style="min-width: 120px; text-align: center; font-size: 0.8rem; position: sticky; top: 0; background: #0d6efd; z-index: 10; color: white;">
            <div>MACHINE</div>
            <div style="font-size: 0.7rem; color: #adb5bd;">Code</div>
        </th>
        <th style="min-width: 120px; text-align: center; font-size: 0.8rem; position: sticky; top: 0; background: #0d6efd; z-index: 10; color: white;">
            <div>EXPENSE</div>
            <div style="font-size: 0.7rem; color: #adb5bd;">Code</div>
        </th>
    `;

    headerHtml += '</tr>';

    // Create grid table body with station categorization
    let bodyHtml = '';
    
    // Use the stations_grid data from the backend response
    const stations = gridData.stations_grid || {};

    Object.keys(stations).forEach(function(stationName) {
        const employees = stations[stationName];

        // Add station header row
        bodyHtml += `
            <tr class="table-secondary">
                <td colspan="${9 + gridData.days_in_month}" style="font-weight: bold; background-color: #6c757d; color: white;">
                    <i class="fas fa-building me-2"></i>${stationName} (${employees.length} employees)
                </td>
            </tr>
        `;

        // Add employees for this station
        employees.forEach(function(employee) {
            // Get charge job data for this employee
            const chargeJobData = getEmployeeChargeJobData(employee.EmployeeName);
            
            let rowHtml = `
                <tr>
                    <td style="position: sticky; left: 0; background: white; z-index: 5; border-right: 2px solid #dee2e6;">${employee.No}</td>
                    <td style="position: sticky; left: 50px; background: white; z-index: 5; border-right: 2px solid #dee2e6;">${employee.EmployeeID}</td>
                    <td style="position: sticky; left: 170px; background: white; z-index: 5; border-right: 2px solid #dee2e6; font-weight: 500; padding-left: 20px;">${employee.EmployeeName}</td>
            `;

            // Track totals for this employee
            let totalWorkingDays = 0;
            let totalRegularHours = 0;
            let totalOvertimeHours = 0;

            // Add working hours for each day
            for (let day = 1; day <= gridData.days_in_month; day++) {
                const date = new Date(gridData.year, gridData.month - 1, day);
                const dayOfWeek = date.getDay();
                const isSaturday = dayOfWeek === 6;
                const hours = employee.days[day.toString()] || '-';
                const cellClass = getWorkingHoursClass(hours, isSaturday, employee, day);
                
                // Calculate totals
                if (hours !== '-' && hours !== 'OFF') {
                    if (hours.includes('|')) {
                        const parts = hours.split('|');
                        if (parts.length === 2) {
                            const regularPart = parts[0].trim().replace(/[()]/g, '');
                            const overtimePart = parts[1].trim().replace(/[()]/g, '');
                            
                            const regularHours = parseFloat(regularPart) || 0;
                            const overtimeHours = overtimePart !== '-' ? (parseFloat(overtimePart) || 0) : 0;
                            
                            if (regularHours > 0 || overtimeHours > 0) {
                                totalWorkingDays++;
                                totalRegularHours += regularHours;
                                totalOvertimeHours += overtimeHours;
                            }
                        }
                    } else {
                        const hoursNum = parseFloat(hours);
                        if (hoursNum > 0) {
                            totalWorkingDays++;
                            totalRegularHours += hoursNum;
                        }
                    }
                }
                
                rowHtml += `<td class="${cellClass}" style="text-align: center; font-size: 0.85rem; padding: 0.25rem;">${hours}</td>`;
            }

            // Add totals cells (3 separate cells)
            rowHtml += `
                <td style="text-align: center; font-size: 0.8rem; padding: 0.25rem; background-color: #e8f5e8; font-weight: bold;">${totalWorkingDays}</td>
                <td style="text-align: center; font-size: 0.8rem; padding: 0.25rem; background-color: #e8f5e8; font-weight: bold;">${totalRegularHours.toFixed(1)}</td>
                <td style="text-align: center; font-size: 0.8rem; padding: 0.25rem; background-color: #e8f5e8; font-weight: bold;">${totalOvertimeHours.toFixed(1)}</td>
            `;

            // Add charge job columns
            rowHtml += `
                <td style="text-align: center; font-size: 0.8rem; padding: 0.25rem; background-color: #e8f5e8; font-weight: bold;">${chargeJobData.task_code}</td>
                <td style="text-align: center; font-size: 0.8rem; padding: 0.25rem; background-color: #e8f5e8; font-weight: bold;">${chargeJobData.machine_code}</td>
                <td style="text-align: center; font-size: 0.8rem; padding: 0.25rem; background-color: #e8f5e8; font-weight: bold;">${chargeJobData.expense_code}</td>
            `;

            rowHtml += '</tr>';
            bodyHtml += rowHtml;
        });
    });

    // Update table
    $('#attendanceTable thead').html(headerHtml);
    $('#attendanceTableBody').html(bodyHtml);

    // Apply grid styling but NO DataTables
    $('#attendanceTable').addClass('attendance-grid');
    $('#attendanceTable').removeClass('dataTable'); // Remove any DataTable classes

    // Ensure DataTables is completely disabled
    console.log('Monthly grid displayed - DataTables completely disabled to prevent conflicts');

    // Use shared summary display function
    displayMonthlyGridSummary(gridData);
}

/**
 * Helper function to determine employee station from name
 * This is a simplified approach - can be enhanced with actual station data lookup
 */
function getEmployeeStationFromName(employeeName) {
    // This is a placeholder - in a real implementation, we would call the backend
    // to get the actual station data. For now, we'll use some simple pattern matching
    const name = employeeName.toUpperCase();
    
    // You can enhance this logic based on your station data patterns
    if (name.includes('DEDY') || name.includes('TIGO') || name.includes('DANANG') || 
        name.includes('JUMHADI') || name.includes('RIDHO') || name.includes('LIO')) {
        return 'STATION GRADER';
    } else if (name.includes('HUSAINI') || name.includes('ADI APRIADI') || name.includes('DAFID')) {
        return 'STATION LOADING RAMP';
    } else if (name.includes('RASTANTO') || name.includes('NURSAMSI') || name.includes('DARMA')) {
        return 'STATION STERILIZER';
    } else if (name.includes('AZRUL') || name.includes('ANDRI JULIANDRI')) {
        return 'STATION THRESING';
    } else if (name.includes('FERDY')) {
        return 'STATION DRIVER PURCHASING DAN GENERAL';
    } else if (name.includes('RIZKY SUGANDA') || name.includes('ANWAR') || name.includes('ARIANTO')) {
        return 'STATION EFB PRESS';
    } else if (name.includes('DERRY') || name.includes('YULIZAR') || name.includes('REZA')) {
        return 'STATION PRESS';
    } else if (name.includes('SYAHRIL') || name.includes('SIAGA') || name.includes('RIVALDI')) {
        return 'STATION CLARIFIKASI';
    } else if (name.includes('ANDRIANO') || name.includes('EDDO') || name.includes('HERU')) {
        return 'STATION KERNEL';
    } else if (name.includes('PORDIMAN') || name.includes('RENDY NOPRIYANDI') || name.includes('ADE PRASETYA')) {
        return 'STATION BOILER';
    } else if (name.includes('RENDY GUSTOYO') || name.includes('WAHYU') || name.includes('RIDHO PURNAMA')) {
        return 'STATION WTP';
    } else if (name.includes('HELMI') || name.includes('FIKRIANSYAH') || name.includes('SUHARTOMO')) {
        return 'STATION ENGINE ROOM';
    } else if (name.includes('NAJMI') || name.includes('IRWAN') || name.includes('CHARYADI')) {
        return 'STATION COMPOUND 9234';
    } else if (name.includes('ICHSANUDIN') || name.includes('ATURI') || name.includes('JAYA SAPUTRA')) {
        return 'STATION LABORATORY 240';
    } else if (name.includes('ANDISON') || name.includes('SOFIYAN')) {
        return 'STATION EFFLUENT';
    }
    
    return 'Unknown';
}

/**
 * Get CSS class for attendance status
 */
function getAttendanceStatusClass(status) {
    switch (status) {
        case '1': return 'table-success'; // Present
        case '-': return 'table-danger';  // Absent
        case 'H': return 'table-warning'; // Half day
        case 'S': return 'table-secondary'; // Saturday
        case 'M': return 'table-info';    // Sunday
        case 'C': return 'table-primary'; // Cuti
        default: return '';
    }
}

/**
 * Get day name in Indonesian (abbreviated)
 */
function getDayNameIndonesian(year, month, day) {
    const date = new Date(year, month - 1, day); // month is 0-indexed in JavaScript
    const dayNames = ['Min', 'Sen', 'Sel', 'Rab', 'Kam', 'Jum', 'Sab']; // Sunday=0, Monday=1, etc.
    return dayNames[date.getDay()];
}

/**
 * Get CSS class for working hours display with enhanced status handling
 */
function getWorkingHoursClass(hours, isSaturday, employee, day) {
    // Get status information if available
    let status = null;
    let checkInOnly = false;
    let checkOutOnly = false;
    let completeRecord = false;
    let regularHours = 0;
    let overtimeHours = 0;
    
    if (employee && employee.days && employee.days[`${day}_status`]) {
        const statusInfo = employee.days[`${day}_status`];
        status = statusInfo.status;
        checkInOnly = statusInfo.check_in_only;
        checkOutOnly = statusInfo.check_out_only;
        completeRecord = statusInfo.complete_record;
        regularHours = statusInfo.regular;
        overtimeHours = statusInfo.overtime;
    }
    
    // Handle special cases first
    if (hours === 'OFF') {
        return 'hours-off';
    } else if (hours === '-') {
        return 'hours-absent';
    }
    
    // Handle incomplete records with specific background colors
    if (checkInOnly) {
        return 'hours-check-in-only';  // Blue background
    } else if (checkOutOnly) {
        return 'hours-check-out-only'; // Purple background
    }
    
    // Handle overtime-only records (new status)
    if (status === 'overtime_only') {
        return 'hours-overtime-only'; // Yellow background
    }
    
    // Handle complete records with enhanced color coding
    if (typeof hours === 'string' && hours.includes('|')) {
        const parts = hours.split('|');
        if (parts.length === 2) {
            const regularPart = parts[0].trim().replace(/[()]/g, '');
            const overtimePart = parts[1].trim().replace(/[()]/g, '');
            
            const parsedRegularHours = parseFloat(regularPart) || 0;
            const parsedOvertimeHours = overtimePart !== '-' ? (parseFloat(overtimePart) || 0) : 0;
            
            // NEW: Check for overtime-only records (0 regular hours but has overtime)
            if (parsedRegularHours === 0 && parsedOvertimeHours > 0) {
                return 'hours-overtime-only'; // Yellow background for overtime-only
            }
            
            // Apply the new color scheme requirements:
            
            // 1. Dark Green: Normal hours > 7 AND both check-in/check-out present
            if (parsedRegularHours > 7 && completeRecord) {
                return 'hours-dark-green';
            }
            
            // 2. Light Green: Normal hours present (any amount) AND no overtime
            if (parsedRegularHours > 0 && parsedOvertimeHours === 0) {
                return 'hours-light-green';
            }
            
            // Handle Saturday vs Weekday thresholds for legacy logic
            const threshold = isSaturday ? 5 : 7;
            
            // Exactly 7 hours normal work (Monday-Friday) - Bright green highlighting
            if (parsedRegularHours === 7 && !isSaturday) {
                return 'hours-full'; // Green - Full regular day
            } else if (parsedRegularHours === 5 && isSaturday) {
                return 'hours-full'; // Green - Full regular day for Saturday
            } else if (parsedRegularHours >= threshold && parsedOvertimeHours === 0) {
                return 'hours-full'; // Green - Full regular hours
            } else if (parsedRegularHours > 0) {
                if (parsedOvertimeHours > 0) {
                    return 'hours-overtime'; // Yellow for overtime
                } else {
                    return 'hours-partial'; // Red for partial hours
                }
            }
        }
    } else if (hours.match(/^\d+(\.\d+)?$/)) {
        // Legacy format: single number
        const hoursNum = parseFloat(hours);
        
        if (isSaturday) {
            if (hoursNum >= 5) {
                return 'hours-full'; // Green - Full day for Saturday
            } else if (hoursNum > 0) {
                return 'hours-partial'; // Red - Partial day for Saturday
            }
        } else {
            if (hoursNum >= 7) {
                return 'hours-full'; // Green - Full day
            } else if (hoursNum > 0) {
                return 'hours-partial'; // Red - Partial day
            }
        }
    }
    
    return 'hours-absent'; // Default for unknown values
}

/**
 * Display monthly report summary and data
 * REMOVED: No longer showing summary reports - only consolidated daily grid with station categorization
 */
/*
function displayMonthlyReport(summary, data) {
    // Function removed - only showing consolidated daily grid
}
*/

/**
 * Handle monthly export
 */
function handleMonthlyExport() {
    if (!currentSelectedMonth) {
        showAlert('Please select a month first', 'warning');
        return;
    }

    const busCode = 'PTRJ'; // Default to PTRJ
    const params = {
        year: currentSelectedMonth.year,
        month: currentSelectedMonth.month,
        bus_code: busCode
    };

    // Create download URL
    const queryString = new URLSearchParams(params).toString();
    const downloadUrl = `/api/export?${queryString}`;

    // Show loading state
    const exportBtn = $('#exportMonthlyBtn');
    const originalText = exportBtn.html();
    exportBtn.html('<i class="fas fa-spinner fa-spin me-1"></i>Exporting...');
    exportBtn.prop('disabled', true);

    // Create temporary link and trigger download
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = '';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // Reset button state after a delay
    setTimeout(function() {
        exportBtn.html(originalText);
        exportBtn.prop('disabled', false);
        showAlert('Monthly export initiated. Download should start shortly.', 'info');
    }, 2000);
}

/**
 * Export grid data to Excel
 */
function exportGridToExcel(year, month) {
    const busCode = 'PTRJ';
    const params = new URLSearchParams({
        year: year,
        month: month,
        bus_code: busCode,
        format: 'excel',
        type: 'grid'
    });

    // Create download URL
    const downloadUrl = `/api/export-grid?${params.toString()}`;
    
    // Show loading state
    const button = event.target;
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Exporting...';
    button.disabled = true;

    // Create temporary link and trigger download
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = '';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // Reset button state
    setTimeout(function() {
        button.innerHTML = originalText;
        button.disabled = false;
        showAlert('Grid export to Excel initiated. Download should start shortly.', 'info');
    }, 2000);
}

/**
 * Export grid data to JSON
 */
function exportGridToJSON(year, month) {
    const busCode = 'PTRJ';
    const params = new URLSearchParams({
        year: year,
        month: month,
        bus_code: busCode,
        format: 'json',
        type: 'grid'
    });

    // Create download URL
    const downloadUrl = `/api/export-grid?${params.toString()}`;
    
    // Show loading state
    const button = event.target;
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Exporting...';
    button.disabled = true;

    // Create temporary link and trigger download
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = '';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // Reset button state
    setTimeout(function() {
        button.innerHTML = originalText;
        button.disabled = false;
        showAlert('Grid export to JSON initiated. Download should start shortly.', 'info');
    }, 2000);
}

/**
 * Export station grid data to Excel
 * REMOVED: Station grid functionality consolidated into main grid
 */
/*
function exportStationGridToExcel(year, month) {
    // Function removed - use main grid export functions
}
*/

/**
 * Export station grid data to JSON
 * REMOVED: Station grid functionality consolidated into main grid
 */
/*
function exportStationGridToJSON(year, month) {
    // Function removed - use main grid export functions
}
*/

/**
 * Toggle sync mode
 */
function toggleSyncMode() {
    syncModeActive = !syncModeActive;
    const button = $('#toggleSyncMode');
    const resultsCard = $('.card').last();
    
    if (syncModeActive) {
        button.removeClass('btn-warning').addClass('btn-sync-active');
        button.html('<i class="fas fa-sync-alt fa-spin me-1"></i>Sync Mode ON');
        resultsCard.addClass('sync-mode-active');
        
        // Show sync controls
        $('#selectAll, #selectAllLabel').show();
        $('#syncToSheetBtn').show();
        
        // Re-render table if data exists
        if (window.currentAttendanceData) {
            displayAttendanceData(window.currentAttendanceData);
        }
        
        showAlert('Sync mode activated. You can now select rows to sync to spreadsheet.', 'info');
    } else {
        button.removeClass('btn-sync-active').addClass('btn-warning');
        button.html('<i class="fas fa-sync me-1"></i>Sync Mode');
        resultsCard.removeClass('sync-mode-active');
        
        // Hide sync controls
        $('#selectAll, #selectAllLabel').hide();
        $('#syncToSheetBtn').hide();
        
        // Clear selections
        selectedRows.clear();
        
        // Re-render table if data exists
        if (window.currentAttendanceData) {
            displayAttendanceData(window.currentAttendanceData);
        }
        
        showAlert('Sync mode deactivated.', 'info');
    }
}

/**
 * Handle select all checkbox
 */
function handleSelectAll() {
    const isChecked = $('#selectAll').prop('checked');
    $('.row-checkbox').prop('checked', isChecked);
    
    if (isChecked) {
        $('.row-checkbox').each(function() {
            const rowIndex = $(this).data('row-id');
            selectedRows.add(rowIndex);
            $(`#row_${rowIndex}`).addClass('row-selected');
        });
    } else {
        selectedRows.clear();
        $('.row-checkbox').each(function() {
            const rowIndex = $(this).data('row-id');
            $(`#row_${rowIndex}`).removeClass('row-selected');
        });
    }
    
    updateSyncButton();
}

/**
 * Handle individual row selection
 */
function handleRowSelection() {
    const checkbox = $(this);
    const rowIndex = checkbox.data('row-id');
    const isChecked = checkbox.prop('checked');
    
    if (isChecked) {
        selectedRows.add(rowIndex);
        $(`#row_${rowIndex}`).addClass('row-selected');
    } else {
        selectedRows.delete(rowIndex);
        $(`#row_${rowIndex}`).removeClass('row-selected');
    }
    
    // Update select all checkbox
    const totalCheckboxes = $('.row-checkbox').length;
    const checkedCheckboxes = $('.row-checkbox:checked').length;
    $('#selectAll').prop('checked', checkedCheckboxes === totalCheckboxes);
    
    updateSyncButton();
}

/**
 * Update sync button state
 */
function updateSyncButton() {
    const syncBtn = $('#syncToSheetBtn');
    if (selectedRows.size > 0) {
        syncBtn.html(`<i class="fas fa-cloud-upload-alt me-1"></i>Sync ${selectedRows.size} Records`);
        syncBtn.prop('disabled', false);
    } else {
        syncBtn.html('<i class="fas fa-cloud-upload-alt me-1"></i>Sync to Spreadsheet');
        syncBtn.prop('disabled', true);
    }
}

/**
 * Sync selected rows to Google Spreadsheet
 */
function syncToSpreadsheet() {
    const syncUrl = $('#syncUrl').val();
    const enableSync = $('#enableSync').prop('checked');
    
    if (!enableSync) {
        showAlert('Please enable sync functionality first.', 'warning');
        return;
    }
    
    if (!syncUrl) {
        showAlert('Please enter Google Apps Script URL first.', 'warning');
        return;
    }
    
    if (selectedRows.size === 0) {
        showAlert('Please select at least one row to sync.', 'warning');
        return;
    }
    
    // Prepare data for sync
    const dataToSync = [];
    selectedRows.forEach(rowIndex => {
        const record = window.currentAttendanceData[rowIndex];
        if (record) {
            dataToSync.push({
                employeeId: record.EmployeeID || '',
                employeeName: record.EmployeeName || '',
                date: record.Date || '',
                dayOfWeek: record.DayOfWeek || '',
                shift: record.Shift || '',
                checkIn: record.CheckIn || '',
                checkOut: record.CheckOut || '',
                regularHours: record.RegularHours || 0,
                overtimeHours: record.OvertimeHours || 0,
                totalHours: (record.RegularHours || 0) + (record.OvertimeHours || 0)
            });
        }
    });
    
    // Show progress
    const syncBtn = $('#syncToSheetBtn');
    const originalText = syncBtn.html();
    syncBtn.html('<i class="fas fa-spinner fa-spin me-1"></i>Syncing...').prop('disabled', true);
    
    // Send data to Flask proxy API
    $.ajax({
        url: '/api/sync-to-spreadsheet',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            sync_url: syncUrl,
            action: 'sync_attendance',
            data: dataToSync
        }),
        success: function(response) {
            console.log('Sync response:', response);
            
            if (response.success) {
                showAlert(`Successfully synced ${dataToSync.length} records to spreadsheet!`, 'success');
                
                // Clear selections after successful sync
                selectedRows.clear();
                $('.row-checkbox').prop('checked', false);
                $('#selectAll').prop('checked', false);
                $('.row-selected').removeClass('row-selected');
            } else {
                showAlert('Sync failed: ' + (response.error || 'Unknown error'), 'danger');
            }
        },
        error: function(xhr, status, error) {
            console.error('Sync error:', error);
            
            let errorMessage = 'Sync failed: ' + error;
            if (xhr.responseJSON && xhr.responseJSON.error) {
                errorMessage = 'Sync failed: ' + xhr.responseJSON.error;
            }
            
            showAlert(errorMessage, 'danger');
        },
        complete: function() {
            syncBtn.html(originalText).prop('disabled', false);
            updateSyncButton();
        }
    });
}

/**
 * Toggle Monthly Grid Sync Mode
 */
function toggleMonthlyGridSyncMode() {
    monthlyGridSyncModeActive = !monthlyGridSyncModeActive;
    const button = $('#toggleMonthlyGridSyncMode');
    const headerButton = $('#toggleMonthlyGridSyncModeHeader');
    const monthlyGridControls = $('#monthlyGridSyncControls');
    const syncActions = $('#monthlyGridSyncActions');
    const resultsCard = $('.card').last();
    
    if (monthlyGridSyncModeActive) {
        // Update both buttons
        button.removeClass('btn-warning').addClass('btn-sync-active');
        button.html('<i class="fas fa-sync-alt fa-spin me-1"></i>Sync Mode ON');
        
        headerButton.removeClass('btn-warning').addClass('btn-sync-active');
        headerButton.html('<i class="fas fa-sync-alt fa-spin me-1"></i>Sync Mode ON');
        
        monthlyGridControls.addClass('monthly-grid-sync-active');
        syncActions.show();
        
        showAlert('Monthly Grid Sync mode activated. Click on row numbers to select data rows.', 'info');
        
        // Re-render grid if data exists
        if (currentMonthlyGridData) {
            displayMonthlyGridWithSync(currentMonthlyGridData);
        }
    } else {
        // Update both buttons
        button.removeClass('btn-sync-active').addClass('btn-warning');
        button.html('<i class="fas fa-sync me-1"></i>Sync Mode');
        
        headerButton.removeClass('btn-sync-active').addClass('btn-warning');
        headerButton.html('<i class="fas fa-sync me-1"></i>Sync Mode');
        
        monthlyGridControls.removeClass('monthly-grid-sync-active');
        syncActions.hide();
        
        // Clear selections
        monthlyGridSelectedRows.clear();
        updateMonthlyGridSyncInfo();
        
        showAlert('Monthly Grid Sync mode deactivated.', 'info');
        
        // Re-render grid if data exists
        if (currentMonthlyGridData) {
            displayMonthlyGrid(currentMonthlyGridData);
        }
    }
}

/**
 * Display Monthly Grid with Sync functionality
 */
function displayMonthlyGridWithSync(gridData) {
    currentMonthlyGridData = gridData;
    
    // Completely disable DataTables for monthly grids
    window.useDataTables = false;
    
    // Destroy any existing DataTable to prevent conflicts
    if (attendanceDataTable) {
        console.log('Destroying existing DataTable for monthly grid with sync...');
        attendanceDataTable.destroy();
        attendanceDataTable = null;
    }
    
    // Create header with checkbox column if sync mode is active
    let headerHtml = '<tr>';
    
    if (monthlyGridSyncModeActive) {
        headerHtml += `
            <th style="width: 50px; position: sticky; left: 0; background: #212529; z-index: 21; text-align: center;">
                <div class="form-check">
                    <input class="form-check-input monthly-grid-checkbox" type="checkbox" id="selectAllMonthlyGrid">
                    <label class="form-check-label text-white" for="selectAllMonthlyGrid" style="font-size: 0.8rem;">All</label>
                </div>
            </th>
        `;
    }
    
    headerHtml += `
        <th style="min-width: 50px; position: sticky; left: ${monthlyGridSyncModeActive ? '50px' : '0'}; background: #212529; z-index: 20; text-align: center; font-size: 0.8rem;">No</th>
        <th style="min-width: 120px; position: sticky; left: ${monthlyGridSyncModeActive ? '120px' : '50px'}; background: #212529; z-index: 20; text-align: center; font-size: 0.8rem;">Employee ID</th>
        <th style="min-width: 200px; position: sticky; left: ${monthlyGridSyncModeActive ? '240px' : '170px'}; background: #212529; z-index: 20; text-align: center; font-size: 0.8rem;">Employee Name</th>
    `;

    // Add day headers
    for (let day = 1; day <= gridData.days_in_month; day++) {
        const date = new Date(gridData.year, gridData.month - 1, day);
        const dayOfWeek = date.getDay();
        const isSunday = dayOfWeek === 0;
        const dayName = getDayNameIndonesian(gridData.year, gridData.month, day);
        
        const headerClass = isSunday ? 'bg-danger' : 'bg-primary';
        const headerStyle = isSunday ? 
            'min-width: 80px; text-align: center; font-size: 0.8rem; position: sticky; top: 0; background: #dc3545; z-index: 10; color: white;' :
            'min-width: 80px; text-align: center; font-size: 0.8rem; position: sticky; top: 0; background: #0d6efd; z-index: 10; color: white;';
        
        headerHtml += `
            <th style="${headerStyle}">
                <div>${day}</div>
                <div style="font-size: 0.6rem; color: #adb5bd;">${dayName}</div>
            </th>
        `;
    }
    
    // Add totals columns
    headerHtml += `
        <th style="min-width: 80px; text-align: center; font-size: 0.8rem; position: sticky; top: 0; background: #198754; z-index: 10; color: white;">
            <div>DAYS</div>
            <div style="font-size: 0.7rem; color: #adb5bd;">Total</div>
        </th>
        <th style="min-width: 80px; text-align: center; font-size: 0.8rem; position: sticky; top: 0; background: #198754; z-index: 10; color: white;">
            <div>REG</div>
            <div style="font-size: 0.7rem; color: #adb5bd;">Hours</div>
        </th>
        <th style="min-width: 80px; text-align: center; font-size: 0.8rem; position: sticky; top: 0; background: #198754; z-index: 10; color: white;">
            <div>OT</div>
            <div style="font-size: 0.7rem; color: #adb5bd;">Hours</div>
        </th>
    `;

    // Add charge job columns (Task Code, Machine Code, Expense Code)
    headerHtml += `
        <th style="min-width: 120px; text-align: center; font-size: 0.8rem; position: sticky; top: 0; background: #0d6efd; z-index: 10; color: white;">
            <div>TASK</div>
            <div style="font-size: 0.7rem; color: #adb5bd;">Code</div>
        </th>
        <th style="min-width: 120px; text-align: center; font-size: 0.8rem; position: sticky; top: 0; background: #0d6efd; z-index: 10; color: white;">
            <div>MACHINE</div>
            <div style="font-size: 0.7rem; color: #adb5bd;">Code</div>
        </th>
        <th style="min-width: 120px; text-align: center; font-size: 0.8rem; position: sticky; top: 0; background: #0d6efd; z-index: 10; color: white;">
            <div>EXPENSE</div>
            <div style="font-size: 0.7rem; color: #adb5bd;">Code</div>
        </th>
    `;

    headerHtml += '</tr>';

    // Create grid table body
    let bodyHtml = '';
    gridData.grid_data.forEach(function(employee, index) {
        // Get charge job data for this employee
        const chargeJobData = getEmployeeChargeJobData(employee.EmployeeName);
        
        const rowId = `monthly_row_${index}`;
        const isSelected = monthlyGridSelectedRows.has(index);
        const rowClass = isSelected ? 'monthly-grid-row-selected' : '';
        const selectableClass = monthlyGridSyncModeActive ? 'monthly-grid-row-selectable' : '';
        
        let rowHtml = `<tr id="${rowId}" data-row-index="${index}" class="${rowClass} ${selectableClass}">`;
        
        // Add checkbox column if sync mode is active
        if (monthlyGridSyncModeActive) {
            rowHtml += `
                <td style="position: sticky; left: 0; background: white; z-index: 5; border-right: 2px solid #dee2e6; text-align: center;">
                    <input type="checkbox" class="form-check-input monthly-grid-row-checkbox" 
                           data-row-index="${index}" ${isSelected ? 'checked' : ''}>
                </td>
            `;
        }
        
        rowHtml += `
            <td style="position: sticky; left: ${monthlyGridSyncModeActive ? '50px' : '0'}; background: white; z-index: 5; border-right: 2px solid #dee2e6;">
                ${monthlyGridSyncModeActive ? 
                    `<span class="monthly-grid-row-number ${isSelected ? 'selected' : ''}" data-row-index="${index}">${employee.No}</span>` :
                    employee.No
                }
            </td>
            <td style="position: sticky; left: ${monthlyGridSyncModeActive ? '120px' : '50px'}; background: white; z-index: 5; border-right: 2px solid #dee2e6;">${employee.EmployeeID}</td>
            <td style="position: sticky; left: ${monthlyGridSyncModeActive ? '240px' : '170px'}; background: white; z-index: 5; border-right: 2px solid #dee2e6; font-weight: 500;">${employee.EmployeeName}</td>
        `;

        // Track totals for this employee
        let totalWorkingDays = 0;
        let totalRegularHours = 0;
        let totalOvertimeHours = 0;

        // Add working hours for each day
        for (let day = 1; day <= gridData.days_in_month; day++) {
            const date = new Date(gridData.year, gridData.month - 1, day);
            const dayOfWeek = date.getDay();
            const isSaturday = dayOfWeek === 6;
            const hours = employee.days[day.toString()] || '-';
            const cellClass = getWorkingHoursClass(hours, isSaturday, employee, day);
            
            // Calculate totals
            if (hours !== '-' && hours !== 'OFF') {
                if (hours.includes('|')) {
                    const parts = hours.split('|');
                    if (parts.length === 2) {
                        const regularPart = parts[0].trim().replace(/[()]/g, '');
                        const overtimePart = parts[1].trim().replace(/[()]/g, '');
                        
                        const regularHours = parseFloat(regularPart) || 0;
                        const overtimeHours = overtimePart !== '-' ? (parseFloat(overtimePart) || 0) : 0;
                        
                        if (regularHours > 0 || overtimeHours > 0) {
                            totalWorkingDays++;
                            totalRegularHours += regularHours;
                            totalOvertimeHours += overtimeHours;
                        }
                    }
                } else {
                    const hoursNum = parseFloat(hours);
                    if (hoursNum > 0) {
                        totalWorkingDays++;
                        totalRegularHours += hoursNum;
                    }
                }
            }
            
            rowHtml += `<td class="${cellClass}" style="text-align: center; font-size: 0.85rem; padding: 0.25rem;">${hours}</td>`;
        }

        // Add totals cells
        rowHtml += `
            <td style="text-align: center; font-size: 0.8rem; padding: 0.25rem; background-color: #e8f5e8; font-weight: bold;">${totalWorkingDays}</td>
            <td style="text-align: center; font-size: 0.8rem; padding: 0.25rem; background-color: #e8f5e8; font-weight: bold;">${totalRegularHours.toFixed(1)}</td>
            <td style="text-align: center; font-size: 0.8rem; padding: 0.25rem; background-color: #e8f5e8; font-weight: bold;">${totalOvertimeHours.toFixed(1)}</td>
        `;

        // Add charge job columns
        rowHtml += `
            <td style="text-align: center; font-size: 0.8rem; padding: 0.25rem; background-color: #e8f5e8; font-weight: bold;">${chargeJobData.task_code}</td>
            <td style="text-align: center; font-size: 0.8rem; padding: 0.25rem; background-color: #e8f5e8; font-weight: bold;">${chargeJobData.machine_code}</td>
            <td style="text-align: center; font-size: 0.8rem; padding: 0.25rem; background-color: #e8f5e8; font-weight: bold;">${chargeJobData.expense_code}</td>
        `;

        rowHtml += '</tr>';
        bodyHtml += rowHtml;
    });

    // Update table
    $('#attendanceTable thead').html(headerHtml);
    $('#attendanceTableBody').html(bodyHtml);

    // Apply grid styling but NO DataTables
    $('#attendanceTable').addClass('attendance-grid');
    $('#attendanceTable').removeClass('dataTable'); // Remove any DataTable classes

    // Ensure DataTables is completely disabled
    console.log('Monthly grid with sync displayed - DataTables completely disabled to prevent conflicts');

    // Bind event handlers for sync mode
    if (monthlyGridSyncModeActive) {
        bindMonthlyGridSyncEvents();
        updateMonthlyGridSyncInfo();
    }

    // Rest of the function remains same as original displayMonthlyGrid
    displayMonthlyGridSummary(gridData);
}

/**
 * Bind Monthly Grid Sync Events
 */
function bindMonthlyGridSyncEvents() {
    // Handle select all checkbox
    $('#selectAllMonthlyGrid').off('change').on('change', function() {
        const isChecked = $(this).prop('checked');
        $('.monthly-grid-row-checkbox').prop('checked', isChecked);
        
        if (isChecked) {
            $('.monthly-grid-row-checkbox').each(function() {
                const rowIndex = parseInt($(this).data('row-index'));
                monthlyGridSelectedRows.add(rowIndex);
                $(`#monthly_row_${rowIndex}`).addClass('monthly-grid-row-selected');
                $(`.monthly-grid-row-number[data-row-index="${rowIndex}"]`).addClass('selected');
            });
        } else {
            monthlyGridSelectedRows.clear();
            $('.monthly-grid-row-checkbox').each(function() {
                const rowIndex = parseInt($(this).data('row-index'));
                $(`#monthly_row_${rowIndex}`).removeClass('monthly-grid-row-selected');
                $(`.monthly-grid-row-number[data-row-index="${rowIndex}"]`).removeClass('selected');
            });
        }
        
        updateMonthlyGridSyncInfo();
    });

    // Handle individual row checkboxes
    $('.monthly-grid-row-checkbox').off('change').on('change', function() {
        const rowIndex = parseInt($(this).data('row-index'));
        const isChecked = $(this).prop('checked');
        
        if (isChecked) {
            monthlyGridSelectedRows.add(rowIndex);
            $(`#monthly_row_${rowIndex}`).addClass('monthly-grid-row-selected');
            $(`.monthly-grid-row-number[data-row-index="${rowIndex}"]`).addClass('selected');
        } else {
            monthlyGridSelectedRows.delete(rowIndex);
            $(`#monthly_row_${rowIndex}`).removeClass('monthly-grid-row-selected');
            $(`.monthly-grid-row-number[data-row-index="${rowIndex}"]`).removeClass('selected');
        }
        
        // Update select all checkbox
        const totalCheckboxes = $('.monthly-grid-row-checkbox').length;
        const checkedCheckboxes = $('.monthly-grid-row-checkbox:checked').length;
        $('#selectAllMonthlyGrid').prop('checked', checkedCheckboxes === totalCheckboxes);
        
        updateMonthlyGridSyncInfo();
    });

    // Handle row number clicks
    $('.monthly-grid-row-number').off('click').on('click', function() {
        const rowIndex = parseInt($(this).data('row-index'));
        const checkbox = $(`.monthly-grid-row-checkbox[data-row-index="${rowIndex}"]`);
        checkbox.prop('checked', !checkbox.prop('checked')).trigger('change');
    });

    // Handle row clicks
    $('.monthly-grid-row-selectable').off('click').on('click', function(e) {
        // Don't trigger if clicking on checkbox
        if ($(e.target).hasClass('monthly-grid-row-checkbox') || $(e.target).hasClass('monthly-grid-row-number')) {
            return;
        }
        
        const rowIndex = parseInt($(this).data('row-index'));
        const checkbox = $(`.monthly-grid-row-checkbox[data-row-index="${rowIndex}"]`);
        checkbox.prop('checked', !checkbox.prop('checked')).trigger('change');
    });
}

/**
 * Update Monthly Grid Sync Info
 */
function updateMonthlyGridSyncInfo() {
    const selectedCount = monthlyGridSelectedRows.size;
    const syncBtn = $('#syncMonthlyGridToSheetBtn');
    const infoSpan = $('#monthlyGridSelectionInfo');
    const detailsSpan = $('#monthlyGridSelectionDetails');
    
    if (selectedCount > 0) {
        infoSpan.text(`${selectedCount} row${selectedCount > 1 ? 's' : ''} selected`);
        detailsSpan.text(`Ready to sync employee data`);
        syncBtn.prop('disabled', false);
        syncBtn.html(`<i class="fas fa-cloud-upload-alt me-1"></i>Upload ${selectedCount} Employee${selectedCount > 1 ? 's' : ''}`);
    } else {
        infoSpan.text('No rows selected');
        detailsSpan.text('');
        syncBtn.prop('disabled', true);
        syncBtn.html('<i class="fas fa-cloud-upload-alt me-1"></i>Upload Selected Data');
    }
}

/**
 * Sync Monthly Grid to Spreadsheet
 */
function syncMonthlyGridToSpreadsheet() {
    const syncUrl = $('#monthlyGridSyncUrl').val();
    const enableSync = $('#enableMonthlyGridSync').prop('checked');
    
    if (!enableSync) {
        showAlert('Please enable monthly grid sync functionality first.', 'warning');
        return;
    }
    
    if (!syncUrl) {
        showAlert('Please enter Google Apps Script URL first.', 'warning');
        return;
    }
    
    if (monthlyGridSelectedRows.size === 0) {
        showAlert('Please select at least one employee row to sync.', 'warning');
        return;
    }
    
    if (!currentMonthlyGridData) {
        showAlert('No grid data available to sync.', 'warning');
        return;
    }
    
    // Prepare daily data for sync (not summary)
    const dataToSync = [];
    monthlyGridSelectedRows.forEach(rowIndex => {
        const employee = currentMonthlyGridData.grid_data[rowIndex];
        if (employee) {
            // Get charge job data for this employee
            const chargeJobData = getEmployeeChargeJobData(employee.EmployeeName);
            
            dataToSync.push({
                no: employee.No,
                employeeId: employee.EmployeeID,
                employeeName: employee.EmployeeName,
                year: currentMonthlyGridData.year,
                month: currentMonthlyGridData.month,
                monthName: getMonthName(currentMonthlyGridData.month),
                daysInMonth: currentMonthlyGridData.days_in_month,
                dailyHours: employee.days,  // Send the actual daily hours data with status info
                // Add charge job data for enhanced sync
                taskCode: chargeJobData.task_code,
                machineCode: chargeJobData.machine_code,
                expenseCode: chargeJobData.expense_code
            });
        }
    });
    
    // Show progress
    const syncBtn = $('#syncMonthlyGridToSheetBtn');
    const originalText = syncBtn.html();
    syncBtn.html('<i class="fas fa-spinner fa-spin me-1"></i>Syncing...').prop('disabled', true);
    
    // Send data to Flask proxy API
    $.ajax({
        url: '/api/sync-to-spreadsheet',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            sync_url: syncUrl,
            action: 'sync_daily_grid',  // Changed action name
            data: dataToSync
        }),
        success: function(response) {
            console.log('Daily Grid Sync response:', response);
            
            if (response.success) {
                showAlert(`Successfully synced ${dataToSync.length} employee daily records to spreadsheet!`, 'success');
                
                // Clear selections after successful sync
                monthlyGridSelectedRows.clear();
                $('.monthly-grid-row-checkbox').prop('checked', false);
                $('#selectAllMonthlyGrid').prop('checked', false);
                $('.monthly-grid-row-selected').removeClass('monthly-grid-row-selected');
                $('.monthly-grid-row-number.selected').removeClass('selected');
            } else {
                showAlert('Sync failed: ' + (response.error || 'Unknown error'), 'danger');
            }
        },
        error: function(xhr, status, error) {
            console.error('Daily Grid Sync error:', error);
            
            let errorMessage = 'Sync failed: ' + error;
            if (xhr.responseJSON && xhr.responseJSON.error) {
                errorMessage = 'Sync failed: ' + xhr.responseJSON.error;
            }
            
            showAlert(errorMessage, 'danger');
        },
        complete: function() {
            syncBtn.html(originalText).prop('disabled', false);
            updateMonthlyGridSyncInfo();
        }
    });
}

/**
 * Display Monthly Grid Summary (separated from main display function)
 */
function displayMonthlyGridSummary(gridData) {
    // Apply grid styling
    $('#attendanceTable').addClass('attendance-grid');

    // Add CSS for fixed header
    const styleId = 'fixedHeaderStyle';
    if (!document.getElementById(styleId)) {
        const style = document.createElement('style');
        style.id = styleId;
        style.innerHTML = `
            .table-container {
                max-height: 600px;
                overflow-y: auto;
                overflow-x: auto;
            }
            .attendance-grid th {
                position: sticky;
                top: 0;
                background-color: #212529;
                color: white;
                z-index: 10;
            }
            .attendance-grid th:nth-child(1),
            .attendance-grid th:nth-child(2),
            .attendance-grid th:nth-child(3) {
                z-index: 20;
            }
        `;
        document.head.appendChild(style);
    }

    // Show summary info
    let summaryHtml = `
        <div class="alert alert-success">
            <h5><i class="fas fa-info-circle me-2"></i>Grid Summary by Station</h5>
            <div class="row">
                <div class="col-md-2">
                    <strong>Total Stations:</strong> ${gridData.total_stations || 'N/A'}
                </div>
                <div class="col-md-2">
                    <strong>Total Employees:</strong> ${gridData.total_employees}
                </div>
                <div class="col-md-2">
                    <strong>Days in Month:</strong> ${gridData.days_in_month}
                </div>
                <div class="col-md-2">
                    <strong>Working Days:</strong> ${gridData.total_working_days}
                </div>
                <div class="col-md-4">
                    <strong>Period:</strong> ${gridData.date_range}
                </div>
            </div>
            <div class="mt-2">
                <small>
                    <strong>Legend:</strong>
                    <span class="badge bg-success">(7) | (-)</span> Full Regular Hours |
                    <span class="badge bg-warning">Partial Hours</span> Incomplete Hours |
                    <span class="badge bg-secondary">-</span> Absent |
                    <span class="badge bg-info">OFF</span> Sunday |
                    <span class="badge bg-danger" style="background-color: #dc3545;">Sunday Header</span> Red Background
                </small>
            </div>
            <div class="mt-3">
                <button class="btn btn-success me-2" onclick="exportGridToExcel(${gridData.year}, ${gridData.month})">
                    <i class="fas fa-file-excel me-1"></i>
                    Export to Excel
                </button>
                <button class="btn btn-info" onclick="exportGridToJSON(${gridData.year}, ${gridData.month})">
                    <i class="fas fa-file-code me-1"></i>
                    Export to JSON
                </button>
            </div>
        </div>
    `;

    // Add overtime summary if available
    if (gridData.overtime_summary && gridData.overtime_summary.length > 0) {
        summaryHtml += `
            <div class="alert alert-warning mt-3">
                <h5><i class="fas fa-clock me-2"></i>Overtime Summary</h5>
                <div class="table-responsive">
                    <table class="table table-sm table-striped">
                        <thead>
                            <tr>
                                <th>No</th>
                                <th>Employee ID</th>
                                <th>Employee Name</th>
                                <th>Regular Hours</th>
                                <th>Overtime Hours</th>
                                <th>Total Hours</th>
                            </tr>
                        </thead>
                        <tbody>
        `;

        gridData.overtime_summary.forEach(function(emp) {
            summaryHtml += `
                <tr>
                    <td>${emp.No}</td>
                    <td>${emp.EmployeeID}</td>
                    <td>${emp.EmployeeName}</td>
                    <td class="text-success">${emp.TotalRegular}</td>
                    <td class="text-warning">${emp.TotalOvertime}</td>
                    <td class="text-primary"><strong>${emp.TotalHours}</strong></td>
                </tr>
            `;
        });

        summaryHtml += `
                        </tbody>
                    </table>
                </div>
                <div class="mt-2">
                    <small class="text-muted">
                        <i class="fas fa-info-circle me-1"></i>
                        Only employees with overtime hours are shown above.
                    </small>
                </div>
            </div>
        `;
    }

    // Create or update grid summary section
    let gridSummarySection = $('#gridSummarySection');
    if (gridSummarySection.length === 0) {
        // Create new section after monthlySummarySection
        $('#monthlySummarySection').after('<div id="gridSummarySection" class="row mb-4"><div class="col-12"></div></div>');
        gridSummarySection = $('#gridSummarySection');
    }

    gridSummarySection.find('.col-12').html(summaryHtml);
    gridSummarySection.show();

    // Scroll to table
    $('html, body').animate({
        scrollTop: $('#attendanceTable').offset().top - 100
    }, 500);
}

/**
 * Load attendance data from API
 */
function loadAttendanceData() {
    showLoading(true);
    
    // Hide monthly grid sync controls for custom reports
    $('#monthlyGridSyncControls').hide();
    $('#monthlyGridSyncToggleContainer').hide();

    const params = {
        start_date: $('#startDate').val(),
        end_date: $('#endDate').val(),
        bus_code: $('#busCode').val(),
        employee_id: $('#employeeSelect').val(),
        shift: $('#shiftSelect').val()
    };

    // Remove empty parameters
    Object.keys(params).forEach(key => {
        if (!params[key]) delete params[key];
    });

    $.ajax({
        url: '/api/attendance',
        method: 'GET',
        data: params,
        success: function(response) {
            if (response.success) {
                displayAttendanceData(response.data);
                showAlert(`Successfully loaded ${response.total_records} records`, 'success');
            } else {
                showAlert('Error loading attendance data: ' + response.error, 'danger');
            }
        },
        error: function(xhr, status, error) {
            showAlert('Failed to load attendance data: ' + error, 'danger');
        },
        complete: function() {
            showLoading(false);
        }
    });
}
