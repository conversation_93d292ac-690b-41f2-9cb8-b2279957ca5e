# Google Spreadsheet Sync Setup Guide

## Overview
This guide will help you set up the Google Spreadsheet synchronization feature for the VenusHR14 Attendance Report System. This feature allows you to select attendance records and sync them directly to a Google Spreadsheet.

## Prerequisites
- Google account with access to Google Sheets and Google Apps Script
- Access to the VenusHR14 Attendance Report System
- Basic understanding of Google Apps Script deployment

## Step-by-Step Setup

### 1. Create a New Google Spreadsheet
1. Go to [Google Sheets](https://sheets.google.com)
2. Click "Create" to create a new spreadsheet
3. Name it something like "VenusHR14 Attendance Data"
4. Note the spreadsheet URL for later reference

### 2. Set Up Google Apps Script
1. Go to [Google Apps Script](https://script.google.com)
2. Click "New Project"
3. Delete the default `myFunction()` code
4. Copy and paste the entire contents of `google_apps_script.js` from this project
5. Save the project with a name like "Attendance Sync Handler"

### 3. Deploy the Apps Script as Web App
1. In the Apps Script editor, click "Deploy" > "New deployment"
2. Choose "Web app" as the type
3. Set the following configuration:
   - **Description**: "Attendance Sync API v1.0"
   - **Execute as**: "Me (your email)"
   - **Who has access**: "Anyone"
4. Click "Deploy"
5. **IMPORTANT**: Copy the deployment URL (it will look like: `https://script.google.com/macros/s/YOUR_SCRIPT_ID/exec`)
6. Click "Done"

### 4. Configure Permissions
1. The first time you deploy, Google will ask for permissions
2. Click "Review permissions"
3. Choose your Google account
4. Click "Advanced" > "Go to [Project Name] (unsafe)" if prompted
5. Click "Allow" to grant the necessary permissions

### 5. Test the Script (Optional)
1. In the Apps Script editor, select the `testScript` function from the dropdown
2. Click the "Run" button
3. Check the execution log to ensure it runs without errors
4. Go to your Google Spreadsheet - you should see a new sheet called "AttendanceData" with test data

### 6. Configure the Attendance Report System
1. Open the VenusHR14 Attendance Report System
2. Go to the "Custom Date Range" tab
3. In the sync configuration section:
   - Paste your Google Apps Script deployment URL in the "Google Apps Script URL" field
   - Check the "Enable sync functionality" checkbox

## Using the Sync Feature

### Activating Sync Mode
1. Generate an attendance report using the custom date range
2. Click the "Sync Mode" button (it will turn orange and show "Sync Mode ON")
3. Checkboxes will appear in the first column of the table
4. The "Select All" checkbox will appear in the header

### Selecting Records
- **Individual Selection**: Click the checkbox next to each record you want to sync
- **Select All**: Use the header checkbox to select/deselect all records
- **Row Click**: Click anywhere on a row (except the checkbox) to toggle its selection
- Selected rows will be highlighted in blue

### Syncing Data
1. Select the records you want to sync
2. Click the "Sync X Records" button (X = number of selected records)
3. The system will send the data to your Google Spreadsheet
4. You'll see a success message when the sync is complete
5. Selected records will be automatically deselected after successful sync

### Deactivating Sync Mode
- Click the "Sync Mode" button again to turn off sync mode
- All selections will be cleared
- Checkboxes will be hidden

## Google Spreadsheet Structure

The synced data will be stored in a sheet called "AttendanceData" with the following columns:

| Column | Header | Description |
|--------|--------|-------------|
| A | Employee ID | Unique employee identifier |
| B | Employee Name | Full name of the employee |
| C | Date | Attendance date (YYYY-MM-DD) |
| D | Day of Week | Day name (Monday, Tuesday, etc.) |
| E | Shift | Work shift information |
| F | Check In | Check-in time |
| G | Check Out | Check-out time |
| H | Regular Hours | Regular working hours |
| I | Overtime Hours | Overtime hours worked |
| J | Total Hours | Total hours (Regular + Overtime) |
| K | Sync Date | Date when record was synced |
| L | Sync Time | Time when record was synced |

## Supported API Actions

The Google Apps Script supports the following actions:

### sync_attendance
- **Purpose**: Sync attendance data to the spreadsheet
- **Method**: GET or POST
- **Parameters**: 
  - `action=sync_attendance`
  - `data=[JSON array of attendance records]`

### get_data
- **Purpose**: Retrieve all synced data from the spreadsheet
- **Method**: GET
- **Parameters**: `action=get_data`

### clear_data
- **Purpose**: Clear all synced data (keep headers)
- **Method**: GET
- **Parameters**: 
  - `action=clear_data`
  - `confirm=yes`

## Troubleshooting

### Common Issues

**1. "Invalid request: missing parameters" Error**
- Ensure the Google Apps Script URL is correctly entered
- Verify that sync functionality is enabled

**2. "Sync failed" Error**
- Check your internet connection
- Verify the Google Apps Script deployment is active
- Try redeploying the Apps Script

**3. No data appears in spreadsheet**
- Check if the "AttendanceData" sheet exists
- Verify the script has proper permissions
- Check the Apps Script execution logs

**4. Permission denied errors**
- Re-run the authorization process in Google Apps Script
- Ensure the script is deployed with "Execute as: Me"

### Testing the Connection

You can test the connection by calling the API directly:

```
https://script.google.com/macros/s/YOUR_SCRIPT_ID/exec?action=get_data
```

This should return a JSON response with the current data or an empty array.

## Security Considerations

1. **URL Protection**: Keep your Google Apps Script URL private
2. **Access Control**: The script is set to "Anyone" access for simplicity, but you can restrict it to specific users if needed
3. **Data Sensitivity**: Be aware that attendance data is being stored in Google Sheets
4. **Regular Backups**: Consider backing up your spreadsheet data regularly

## Advanced Configuration

### Custom Spreadsheet Name
To use a different sheet name, modify the `sheetName` variable in the Apps Script:
```javascript
let sheet = spreadsheet.getSheetByName('YourCustomSheetName');
```

### Data Validation
The script includes basic data validation. You can extend this by modifying the `handleAttendanceSync` function.

### Large Data Sets
For syncing large amounts of data (>1000 records), consider using the POST method instead of GET to avoid URL length limitations.

## Support

If you encounter issues:
1. Check the Google Apps Script execution logs
2. Verify all setup steps were completed correctly
3. Test with a small dataset first
4. Check browser console for any JavaScript errors

## Version History

- **v2.0**: Enhanced error handling, improved data formatting, added multiple action support
- **v1.0**: Initial release with basic sync functionality 